# Git
.git
.gitignore
.gitattributes

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
*.md
docs/
README*

# Development files
.env.example
.env.local
.env.development
.env.test

# Logs
logs/
*.log
osint_framework.log

# Temporary files
tmp/
temp/
.tmp/

# Node modules (if any)
node_modules/

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Output directories (will be mounted as volumes)
output/
data/crawled_docs/
data/vector_index/
data/cti_index/

# Test files
test_*.py
tests/
*_test.py

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Build artifacts
build/
dist/
*.egg-info/

# Coverage reports
htmlcov/

# Backup files
*.bak
*.backup
