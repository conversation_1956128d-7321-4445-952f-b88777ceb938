name: 🧠 OSINT Framework CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'
  
jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        test-suite: 
          - "Agent Tests"
          - "Tools Tests" 
          - "Security Tools Tests"
          - "Monitoring Tests"
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          tesseract-ocr \
          tesseract-ocr-eng \
          libgl1-mesa-glx \
          libglib2.0-0 \
          libsm6 \
          libxext6 \
          libxrender-dev \
          libgomp1 \
          libglib2.0-0
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov
    
    - name: Install Playwright browsers
      run: |
        playwright install chromium
        playwright install firefox
      continue-on-error: true
    
    - name: Create test directories
      run: |
        mkdir -p tests/data
        mkdir -p tests/results
        mkdir -p logs
        mkdir -p data
    
    - name: Run specific test suite
      env:
        OSINT_TEST_MODE: "ci"
        OSINT_LOG_LEVEL: "INFO"
      run: |
        python scripts/run_tests.py --suite "${{ matrix.test-suite }}" --verbose
      continue-on-error: true
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.test-suite }}
        path: |
          tests/results/
          logs/
        retention-days: 30
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      if: matrix.test-suite == 'Agent Tests'
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
      continue-on-error: true

  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' || github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run integration tests
      env:
        OSINT_TEST_MODE: "integration"
        SERPER_API_KEY: ${{ secrets.SERPER_API_KEY }}
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      run: |
        python scripts/run_tests.py --verbose
      continue-on-error: true
    
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: |
          tests/results/
          logs/
        retention-days: 30

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Bandit security scan
      uses: securecodewarrior/github-action-bandit@v1
      with:
        config_file: '.bandit'
        recursive: true
        exclude_paths: 'tests/,docs/'
      continue-on-error: true
    
    - name: Run Safety check
      run: |
        pip install safety
        safety check --json --output safety-report.json
      continue-on-error: true
    
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-scan-results
        path: |
          bandit-report.json
          safety-report.json
        retention-days: 30

  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install quality tools
      run: |
        python -m pip install --upgrade pip
        pip install black isort flake8 mypy pylint
    
    - name: Check code formatting with Black
      run: |
        black --check --diff .
      continue-on-error: true
    
    - name: Check import sorting with isort
      run: |
        isort --check-only --diff .
      continue-on-error: true
    
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
      continue-on-error: true
    
    - name: Type check with mypy
      run: |
        mypy . --ignore-missing-imports --no-strict-optional
      continue-on-error: true

  performance-test:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark
    
    - name: Run performance tests
      env:
        OSINT_TEST_MODE: "performance"
      run: |
        python -m pytest tests/ -k "performance" --benchmark-json=benchmark.json
      continue-on-error: true
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-results
        path: benchmark.json
        retention-days: 30

  docker-test:
    name: Docker Build Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      run: |
        docker build -t osint-framework:test .
      continue-on-error: true
    
    - name: Test Docker container
      run: |
        docker run --rm osint-framework:test python --version
        docker run --rm osint-framework:test python -c "import agents.geo_agent; print('Import successful')"
      continue-on-error: true

  deploy-docs:
    name: Deploy Documentation
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [test, code-quality]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install documentation dependencies
      run: |
        python -m pip install --upgrade pip
        pip install mkdocs mkdocs-material mkdocs-mermaid2-plugin
    
    - name: Build documentation
      run: |
        mkdocs build
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./site

  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [test, integration-test, security-scan, code-quality]
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.test.result == 'success' && needs.code-quality.result == 'success'
      run: |
        echo "✅ All tests passed successfully!"
    
    - name: Notify on failure
      if: needs.test.result == 'failure' || needs.code-quality.result == 'failure'
      run: |
        echo "❌ Some tests failed. Check the logs for details."
        exit 1
