# CrewAI OSINT Agent Framework - Development Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBUG_MODE=true

# Set working directory
WORKDIR /app

# Install system dependencies including development tools
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    chromium \
    chromium-driver \
    && rm -rf /var/lib/apt/lists/*

# Set Chrome environment variables for Selenium
ENV CHROME_BIN=/usr/bin/chromium \
    CHROMEDRIVER_PATH=/usr/bin/chromedriver

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies including development packages
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir jupyter ipython pytest-cov

# Create necessary directories
RUN mkdir -p data/crawled_docs data/vector_index data/cti_index output/reports

# Expose ports for API, UI, and Jupyter
EXPOSE 8000 8501 8888

# Default command for development (interactive shell)
CMD ["/bin/bash"]
