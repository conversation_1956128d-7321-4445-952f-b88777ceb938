# 🧠 CrewAI OSINT Agent Framework

A sophisticated multi-agent OSINT (Open Source Intelligence) framework built with CrewAI, featuring advanced AI agents, comprehensive tooling, and enterprise-grade capabilities for intelligence gathering and analysis.

## 🌟 Features

### Core Capabilities
- **Multi-Agent Architecture**: Specialized agents for different OSINT domains
- **Advanced Search Integration**: Serper API for comprehensive web search
- **Intelligent Web Crawling**: Crawl4AI for content extraction and analysis
- **Multi-Modal Analysis**: Image and video analysis capabilities
- **Browser Automation**: Stealth web scraping and interaction
- **Workflow Orchestration**: Complex multi-agent workflows with event-driven processing
- **Memory Management**: Persistent conversation history and context with LangGraph
- **Security & Privacy**: Encryption, access controls, and local embeddings
- **Real-time Monitoring**: Health checks, metrics, and alerting
- **RESTful API**: Complete API interface for all functionality
- **Automated Testing**: Comprehensive testing pipeline for quality assurance

### Specialized Agents
- **Geopolitical Intelligence Agent**: Regional analysis and geopolitical insights
- **Cyber Threat Intelligence Agent**: Threat actor tracking and IOC analysis
- **Multi-Modal OSINT Agent**: Image/video analysis with OSINT context
- **Browser OSINT Agent**: Advanced web investigation with stealth capabilities
- **Stateful Agents**: Memory-persistent agents with conversation context

### Advanced Features
- **Multi-Modal Analysis**: OCR, face recognition, object detection, forensic analysis
- **Browser Automation**: Selenium, Playwright, undetected Chrome for stealth operations
- **Workflow Engine**: Event-driven workflows with conditional logic and parallel processing
- **Security Suite**: Data encryption, user authentication, privacy protection
- **Local Embeddings**: Privacy-preserving text analysis without external APIs
- **Automated Testing**: Continuous evaluation and quality assurance pipeline

### Enterprise Features
- **Docker Containerization**: Easy deployment and scaling
- **Comprehensive Logging**: Structured logging with multiple outputs
- **Health Monitoring**: Real-time system health and performance metrics
- **API Documentation**: Auto-generated OpenAPI/Swagger documentation
- **Configuration Management**: Environment-based configuration
- **CI/CD Pipeline**: GitHub Actions for automated testing and deployment

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- Docker (optional)
- API Keys:
  - OpenAI API key
  - Serper API key

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-org/crewai-osint-framework.git
cd crewai-osint-framework
```

2. **Set up environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **Configure environment variables**
```bash
cp .env.example .env
# Edit .env with your API keys
```

4. **Install system dependencies (for multi-modal analysis)**
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr tesseract-ocr-eng libgl1-mesa-glx

# macOS
brew install tesseract

# Install Playwright browsers
playwright install chromium firefox
```

5. **Run the framework**
```bash
# Start the API server
python -m uvicorn api.main:app --host 0.0.0.0 --port 8000

# Or use Docker
docker-compose up -d
```

## 📖 Usage Examples

### Basic Agent Usage

```python
from agents.geo_agent import GeopoliticalAgent
from agents.multimodal_osint_agent import MultiModalOSINTAgent
from agents.browser_osint_agent import BrowserOSINTAgent

# Geopolitical analysis
geo_agent = GeopoliticalAgent()
result = geo_agent.generate_intelligence_brief(
    topic="Regional security analysis",
    regions=["Middle East", "Eastern Europe"],
    time_range="30d"
)

# Multi-modal analysis
multimodal_agent = MultiModalOSINTAgent()
result = multimodal_agent.analyze_media_with_context(
    media_input="path/to/image.jpg",
    analysis_types=["metadata", "ocr", "faces"],
    osint_context=True
)

# Browser investigation
browser_agent = BrowserOSINTAgent(headless=True, stealth=True)
result = browser_agent.investigate_target(
    target="suspicious-domain.com",
    investigation_type="comprehensive"
)
```

### Workflow Orchestration

```python
from workflows.workflow_builder import WorkflowBuilder, WorkflowTemplates
from workflows.workflow_engine import workflow_engine

# Create comprehensive investigation workflow
builder = WorkflowTemplates.comprehensive_target_investigation("target.com")
workflow_id = builder.register()

# Execute workflow
execution_id = await workflow_engine.execute_workflow(
    workflow_id=workflow_id,
    initial_context={"priority": "high"}
)

# Monitor execution
execution = workflow_engine.get_workflow_status(execution_id)
print(f"Status: {execution.status}")
```

### API Usage

```bash
# Health check
curl http://localhost:8000/health

# Multi-modal analysis
curl -X POST "http://localhost:8000/api/v1/multimodal/analyze-upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@image.jpg"

# Browser investigation
curl -X POST "http://localhost:8000/api/v1/browser/investigate" \
  -H "Content-Type: application/json" \
  -d '{
    "target": "example.com",
    "investigation_type": "comprehensive",
    "use_stealth": true
  }'

# Execute workflow
curl -X POST "http://localhost:8000/api/v1/workflows/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "workflow-uuid",
    "initial_context": {"priority": "high"}
  }'
```

## 🏗️ Architecture

### Multi-Agent Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Geo Agent     │    │   CTI Agent     │    │ MultiModal      │    │  Browser Agent  │
│                 │    │                 │    │ Agent           │    │                 │
│ • Regional      │    │ • Threat        │    │ • Image/Video   │    │ • Web Scraping  │
│   Analysis      │    │   Tracking      │    │   Analysis      │    │ • Stealth Mode  │
│ • Geopolitical  │    │ • IOC           │    │ • OCR/Face      │    │ • Social Media  │
│   Intelligence  │    │   Extraction    │    │   Recognition   │    │   Investigation │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         └───────────────────────┼───────────────────────┼───────────────────────┘
                                 │                       │
                    ┌─────────────────┐    ┌─────────────────┐
                    │   Tool Layer    │    │ Workflow Engine │
                    │                 │    │                 │
                    │ • Serper API    │    │ • Event-Driven  │
                    │ • Crawl4AI      │    │ • Parallel Exec │
                    │ • Multi-Modal   │    │ • Conditional   │
                    │ • Browser Auto  │    │   Logic         │
                    └─────────────────┘    └─────────────────┘
```

### System Components
- **Agents**: Specialized AI agents for different OSINT domains
- **Tools**: External service integrations and utilities
- **Workflows**: Complex multi-agent orchestration engine
- **Memory**: LangGraph-based persistent conversation management
- **Security**: Encryption, authentication, and privacy protection
- **API**: RESTful interface for all functionality
- **Monitoring**: Health checks, metrics, and logging
- **Testing**: Automated testing pipeline for quality assurance

## 🔧 Configuration

### Environment Variables
```bash
# API Keys
OPENAI_API_KEY=your_openai_api_key
SERPER_API_KEY=your_serper_api_key

# Application Settings
LOG_LEVEL=INFO
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000

# Security Settings
OSINT_MASTER_KEY=your_encryption_key
OSINT_SECRET_KEY=your_jwt_secret

# Memory Settings
MEMORY_BACKEND=langgraph
MEMORY_PATH=./data/memory

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
```

### Docker Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  osint-framework:
    build: .
    ports:
      - "8000:8000"
      - "9090:9090"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SERPER_API_KEY=${SERPER_API_KEY}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
  
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
  
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: osint_framework
      POSTGRES_USER: osint
      POSTGRES_PASSWORD: secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 📊 Monitoring

### Health Endpoints
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed system health
- `GET /metrics` - Prometheus metrics

### Logging
- Structured JSON logging
- Multiple log levels (DEBUG, INFO, WARNING, ERROR)
- File and console output
- Request/response logging

### Metrics
- Request count and duration
- Agent execution metrics
- System resource usage
- Error rates and types

## 🧪 Testing

```bash
# Run all tests
python scripts/run_tests.py

# Run specific test suites
python scripts/run_tests.py --suite "Agent Tests"
python scripts/run_tests.py --suite "Tools Tests"

# Run with verbose output
python scripts/run_tests.py --verbose

# List available test suites
python scripts/run_tests.py --list
```

## 🔒 Security Features

### Authentication & Authorization
- JWT-based session management
- Role-based access control
- User creation and management
- Session validation and expiration

### Data Protection
- AES encryption for sensitive data
- Secure file deletion
- Privacy-focused data handling
- Local embeddings for privacy

### Security Scanning
- Sensitive data detection
- Text sanitization
- Privacy report generation
- Secure configuration management

## 🔄 Workflow System

### Workflow Templates
- **Comprehensive Investigation**: Complete target analysis
- **Threat Monitoring**: Continuous threat intelligence pipeline
- **Social Media Monitoring**: Multi-platform social analysis
- **Batch Domain Analysis**: Parallel domain investigation
- **Incident Response**: Automated incident handling

### Workflow Features
- Event-driven execution
- Conditional logic and branching
- Parallel task processing
- Retry mechanisms and error handling
- Webhook integrations
- Scheduled execution (cron-like)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup
```bash
# Install development dependencies
pip install -r requirements.txt

# Run pre-commit hooks
pre-commit install

# Run linting and formatting
black .
isort .
flake8 .
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [CrewAI](https://github.com/joaomdmoura/crewAI) for the multi-agent framework
- [LangChain](https://github.com/hwchase17/langchain) for LLM integration
- [LangGraph](https://github.com/langchain-ai/langgraph) for stateful workflows
- [Serper](https://serper.dev/) for search API
- [Crawl4AI](https://github.com/unclecode/crawl4ai) for web crawling

## 📞 Support

- Documentation: [https://your-docs-site.com](https://your-docs-site.com)
- Issues: [GitHub Issues](https://github.com/your-org/crewai-osint-framework/issues)
- Discussions: [GitHub Discussions](https://github.com/your-org/crewai-osint-framework/discussions)

---

**⚠️ Disclaimer**: This framework is for educational and authorized security research purposes only. Users are responsible for ensuring compliance with applicable laws and regulations.
