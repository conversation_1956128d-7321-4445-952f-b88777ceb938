"""
🧠 CrewAI OSINT Agent Framework - Browser-Enhanced OSINT Agent

Advanced OSINT agent with browser automation capabilities for deep web investigation,
social media analysis, and dynamic content extraction.
"""

import asyncio
import json
import sys
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from agents.base_agent import BaseOSINTAgent
from tools.browser_automation import PLAYWRIGHT_AVAILABLE, BrowserAutomation, PlaywrightAutomation
from tools.serper_wrapper import SerperSearchTool
from utils.logging_config import log_execution_time, osint_logger
from utils.memory_manager import StatefulAgent


class BrowserOSINTAgent(StatefulAgent):
    """OSINT agent with advanced browser automation capabilities"""

    def __init__(
        self,
        user_id: str = "default",
        browser_type: str = "chrome",
        headless: bool = True,
        stealth: bool = True,
    ):
        super().__init__(agent_type="browser_osint", user_id=user_id)

        self.browser_type = browser_type
        self.headless = headless
        self.stealth = stealth

        self.serper = SerperSearchTool()
        self.investigation_history = []

        osint_logger.logger.info(
            "Browser OSINT agent initialized",
            user_id=user_id,
            browser_type=browser_type,
            stealth=stealth,
        )

    @log_execution_time("browser_osint_investigation")
    def investigate_target(
        self, target: str, investigation_type: str = "comprehensive", use_stealth: bool = True
    ) -> Dict[str, Any]:
        """Comprehensive target investigation using browser automation"""

        # Start conversation if not already started
        if not self.conversation_id:
            initial_context = {
                "target": target,
                "investigation_type": investigation_type,
                "started_at": datetime.now().isoformat(),
            }
            self.start_conversation(initial_context)

        result = {
            "target": target,
            "investigation_type": investigation_type,
            "timestamp": datetime.now().isoformat(),
            "search_results": {},
            "web_analysis": {},
            "social_media_analysis": {},
            "technical_analysis": {},
            "threat_indicators": [],
            "conversation_context": {},
        }

        try:
            # Add target to conversation
            self.add_user_message(
                content=f"Investigating target: {target}",
                metadata={"investigation_type": investigation_type, "target": target},
            )

            # Get conversation context
            context = self.get_conversation_context()
            result["conversation_context"] = {
                "conversation_id": self.conversation_id,
                "message_count": context.get("message_count", 0),
                "relevant_memories": len(context.get("relevant_memories", [])),
            }

            # Phase 1: Search engine reconnaissance
            result["search_results"] = self._perform_search_reconnaissance(target)

            # Phase 2: Web analysis with browser automation
            result["web_analysis"] = self._perform_web_analysis(target, use_stealth)

            # Phase 3: Social media analysis
            if investigation_type in ["comprehensive", "social_media"]:
                result["social_media_analysis"] = self._perform_social_media_analysis(target)

            # Phase 4: Technical analysis
            if investigation_type in ["comprehensive", "technical"]:
                result["technical_analysis"] = self._perform_technical_analysis(target)

            # Phase 5: Threat indicator analysis
            result["threat_indicators"] = self._analyze_threat_indicators(result)

            # Store investigation result
            self.add_agent_response(
                content=f"Investigation completed for target: {target}", analysis_data=result
            )

            # Update conversation context
            self.update_context(
                {
                    "last_investigation": {
                        "target": target,
                        "type": investigation_type,
                        "timestamp": datetime.now().isoformat(),
                        "findings_count": len(result.get("threat_indicators", [])),
                    }
                }
            )

            self.investigation_history.append(result)

            osint_logger.logger.info(
                "Browser OSINT investigation completed",
                target=target,
                investigation_type=investigation_type,
                conversation_id=self.conversation_id,
            )

        except Exception as e:
            osint_logger.log_error(e, "browser_osint_investigation")
            result["error"] = str(e)

        return result

    def _perform_search_reconnaissance(self, target: str) -> Dict[str, Any]:
        """Perform initial search engine reconnaissance"""

        search_results = {
            "general_search": {},
            "news_search": {},
            "image_search": {},
            "domain_search": {},
        }

        try:
            # General web search
            search_results["general_search"] = self.serper.search(target, search_type="search")

            # News search
            search_results["news_search"] = self.serper.search(target, search_type="news")

            # Image search
            search_results["image_search"] = self.serper.search(target, search_type="images")

            # Domain-specific searches if target looks like a domain
            if "." in target and " " not in target:
                domain_queries = [f"site:{target}", f"inurl:{target}", f"intitle:{target}"]

                search_results["domain_search"] = {}
                for query in domain_queries:
                    try:
                        search_results["domain_search"][query] = self.serper.search(
                            query, search_type="search"
                        )
                    except Exception as e:
                        search_results["domain_search"][query] = {"error": str(e)}

        except Exception as e:
            search_results["error"] = str(e)

        return search_results

    def _perform_web_analysis(self, target: str, use_stealth: bool = True) -> Dict[str, Any]:
        """Perform web analysis using browser automation"""

        web_analysis = {
            "direct_access": {},
            "related_sites": [],
            "technology_stack": {},
            "security_headers": {},
        }

        # Determine if target is a URL or needs to be searched
        if target.startswith(("http://", "https://")):
            urls_to_analyze = [target]
        elif "." in target and " " not in target:
            # Assume it's a domain
            urls_to_analyze = [f"https://{target}", f"http://{target}"]
        else:
            # Search for the target and analyze top results
            try:
                search_results = self.serper.search(target, search_type="search")
                urls_to_analyze = []

                for result in search_results.get("organic", [])[:3]:
                    url = result.get("link")
                    if url:
                        urls_to_analyze.append(url)
            except:
                urls_to_analyze = []

        # Analyze each URL
        for url in urls_to_analyze:
            try:
                with BrowserAutomation(
                    browser_type=self.browser_type, headless=self.headless, stealth=use_stealth
                ) as browser:

                    # Basic page scraping
                    page_data = browser.scrape_page(
                        url,
                        extract_elements=[
                            "title",
                            "meta[name='description']",
                            "h1",
                            "h2",
                            "a[href]",
                            "img[src]",
                            "script[src]",
                        ],
                    )

                    web_analysis["direct_access"][url] = page_data

                    # Extract technology indicators
                    if "page_data" in page_data:
                        web_analysis["technology_stack"][url] = self._analyze_technology_stack(
                            page_data
                        )

                    # Security analysis
                    web_analysis["security_headers"][url] = self._analyze_security_headers(
                        page_data
                    )

            except Exception as e:
                web_analysis["direct_access"][url] = {"error": str(e)}

        return web_analysis

    def _perform_social_media_analysis(self, target: str) -> Dict[str, Any]:
        """Perform social media analysis"""

        social_analysis = {"platform_searches": {}, "profile_analysis": {}, "content_analysis": {}}

        # Social media platforms to search
        platforms = {
            "twitter": f"site:twitter.com {target}",
            "linkedin": f"site:linkedin.com {target}",
            "facebook": f"site:facebook.com {target}",
            "instagram": f"site:instagram.com {target}",
            "youtube": f"site:youtube.com {target}",
            "github": f"site:github.com {target}",
        }

        try:
            for platform, query in platforms.items():
                try:
                    search_result = self.serper.search(query, search_type="search")
                    social_analysis["platform_searches"][platform] = search_result

                    # Analyze top results for each platform
                    if search_result.get("organic"):
                        top_result = search_result["organic"][0]
                        profile_url = top_result.get("link")

                        if profile_url:
                            social_analysis["profile_analysis"][platform] = (
                                self._analyze_social_profile(platform, profile_url)
                            )

                except Exception as e:
                    social_analysis["platform_searches"][platform] = {"error": str(e)}

        except Exception as e:
            social_analysis["error"] = str(e)

        return social_analysis

    def _analyze_social_profile(self, platform: str, profile_url: str) -> Dict[str, Any]:
        """Analyze a social media profile"""

        profile_analysis = {
            "platform": platform,
            "url": profile_url,
            "accessible": False,
            "content": {},
            "metadata": {},
        }

        try:
            with BrowserAutomation(
                browser_type=self.browser_type, headless=self.headless, stealth=True
            ) as browser:

                page_data = browser.scrape_page(profile_url)

                if "error" not in page_data:
                    profile_analysis["accessible"] = True
                    profile_analysis["content"] = page_data.get("page_data", {})
                    profile_analysis["metadata"] = page_data.get("metadata", {})

                    # Platform-specific analysis
                    if platform == "twitter":
                        profile_analysis["twitter_specific"] = self._analyze_twitter_profile(
                            page_data
                        )
                    elif platform == "linkedin":
                        profile_analysis["linkedin_specific"] = self._analyze_linkedin_profile(
                            page_data
                        )
                else:
                    profile_analysis["error"] = page_data["error"]

        except Exception as e:
            profile_analysis["error"] = str(e)

        return profile_analysis

    def _perform_technical_analysis(self, target: str) -> Dict[str, Any]:
        """Perform technical analysis of the target"""

        technical_analysis = {
            "dns_analysis": {},
            "subdomain_enumeration": {},
            "port_scanning": {},
            "ssl_analysis": {},
        }

        # This would integrate with technical OSINT tools
        # For now, we'll do basic web-based technical analysis

        try:
            # DNS analysis through web services
            dns_queries = [
                f"site:dnsdumpster.com {target}",
                f"site:securitytrails.com {target}",
                f"site:virustotal.com {target}",
            ]

            for query in dns_queries:
                try:
                    result = self.serper.search(query, search_type="search")
                    service_name = query.split("site:")[1].split(" ")[0]
                    technical_analysis["dns_analysis"][service_name] = result
                except:
                    continue

            # Subdomain enumeration through search engines
            subdomain_queries = [f"site:*.{target}", f"inurl:{target} -site:{target}"]

            for query in subdomain_queries:
                try:
                    result = self.serper.search(query, search_type="search")
                    technical_analysis["subdomain_enumeration"][query] = result
                except:
                    continue

        except Exception as e:
            technical_analysis["error"] = str(e)

        return technical_analysis

    def _analyze_technology_stack(self, page_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze technology stack from page data"""

        tech_stack = {"frameworks": [], "cms": [], "analytics": [], "cdn": [], "server_info": []}

        try:
            page_source = page_data.get("page_data", {}).get("page_source", "").lower()

            # Framework detection
            frameworks = {
                "react": ["react", "_react"],
                "angular": ["angular", "ng-"],
                "vue": ["vue.js", "__vue__"],
                "jquery": ["jquery", "$"],
                "bootstrap": ["bootstrap", "btn-"],
            }

            for framework, indicators in frameworks.items():
                if any(indicator in page_source for indicator in indicators):
                    tech_stack["frameworks"].append(framework)

            # CMS detection
            cms_indicators = {
                "wordpress": ["wp-content", "wp-includes"],
                "drupal": ["drupal", "sites/default"],
                "joomla": ["joomla", "option=com_"],
                "shopify": ["shopify", "cdn.shopify"],
            }

            for cms, indicators in cms_indicators.items():
                if any(indicator in page_source for indicator in indicators):
                    tech_stack["cms"].append(cms)

            # Analytics detection
            analytics_indicators = {
                "google_analytics": ["google-analytics", "gtag"],
                "facebook_pixel": ["facebook.net", "fbevents"],
                "hotjar": ["hotjar"],
                "mixpanel": ["mixpanel"],
            }

            for analytics, indicators in analytics_indicators.items():
                if any(indicator in page_source for indicator in indicators):
                    tech_stack["analytics"].append(analytics)

        except Exception as e:
            tech_stack["error"] = str(e)

        return tech_stack

    def _analyze_security_headers(self, page_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze security headers and indicators"""

        security_analysis = {"security_headers": {}, "vulnerabilities": [], "security_score": 0}

        try:
            # This would analyze HTTP headers if available
            # For now, we'll do basic security indicator analysis

            page_source = page_data.get("page_data", {}).get("page_source", "").lower()

            # Check for common security issues
            security_issues = {
                "mixed_content": "http://" in page_source and "https://" in page_source,
                "inline_scripts": "<script>" in page_source,
                "external_scripts": any(
                    domain in page_source for domain in ["googleapis.com", "cdnjs.com"]
                ),
                "form_without_csrf": "<form" in page_source and "csrf" not in page_source,
            }

            for issue, detected in security_issues.items():
                if detected:
                    security_analysis["vulnerabilities"].append(issue)

            # Calculate basic security score
            total_checks = len(security_issues)
            failed_checks = len(security_analysis["vulnerabilities"])
            security_analysis["security_score"] = max(
                0, (total_checks - failed_checks) / total_checks * 100
            )

        except Exception as e:
            security_analysis["error"] = str(e)

        return security_analysis

    def _analyze_twitter_profile(self, page_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze Twitter-specific profile data"""

        return {
            "profile_type": "twitter",
            "analysis": "Twitter profile analysis would be implemented here",
        }

    def _analyze_linkedin_profile(self, page_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze LinkedIn-specific profile data"""

        return {
            "profile_type": "linkedin",
            "analysis": "LinkedIn profile analysis would be implemented here",
        }

    def _analyze_threat_indicators(
        self, investigation_result: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Analyze investigation results for threat indicators"""

        indicators = []

        try:
            # Analyze web analysis results
            web_analysis = investigation_result.get("web_analysis", {})

            for url, analysis in web_analysis.get("direct_access", {}).items():
                if "error" in analysis:
                    continue

                # Check for suspicious redirects
                current_url = analysis.get("page_data", {}).get("current_url", "")
                if current_url != url:
                    indicators.append(
                        {
                            "type": "suspicious_redirect",
                            "url": url,
                            "redirected_to": current_url,
                            "confidence": 0.6,
                            "description": "URL redirects to different domain",
                        }
                    )

                # Check for anti-bot measures
                anti_bot = analysis.get("anti_bot_detection", {})
                if anti_bot.get("cloudflare_detected") or anti_bot.get("captcha_detected"):
                    indicators.append(
                        {
                            "type": "anti_bot_measures",
                            "url": url,
                            "measures": anti_bot.get("indicators", []),
                            "confidence": 0.7,
                            "description": "Site uses anti-bot protection",
                        }
                    )

            # Analyze security findings
            for url, security in web_analysis.get("security_headers", {}).items():
                vulnerabilities = security.get("vulnerabilities", [])
                if vulnerabilities:
                    indicators.append(
                        {
                            "type": "security_vulnerabilities",
                            "url": url,
                            "vulnerabilities": vulnerabilities,
                            "confidence": 0.8,
                            "description": f"Security issues detected: {', '.join(vulnerabilities)}",
                        }
                    )

            # Analyze social media findings
            social_analysis = investigation_result.get("social_media_analysis", {})

            for platform, profile in social_analysis.get("profile_analysis", {}).items():
                if not profile.get("accessible", False):
                    indicators.append(
                        {
                            "type": "inaccessible_profile",
                            "platform": platform,
                            "url": profile.get("url", ""),
                            "confidence": 0.4,
                            "description": f"{platform} profile not accessible",
                        }
                    )

        except Exception as e:
            indicators.append(
                {
                    "type": "analysis_error",
                    "error": str(e),
                    "confidence": 0.1,
                    "description": "Error during threat indicator analysis",
                }
            )

        return indicators

    async def investigate_target_advanced(
        self, target: str, use_playwright: bool = True
    ) -> Dict[str, Any]:
        """Advanced investigation using Playwright for dynamic content"""

        if not PLAYWRIGHT_AVAILABLE or not use_playwright:
            return self.investigate_target(target)

        result = {
            "target": target,
            "timestamp": datetime.now().isoformat(),
            "advanced_analysis": {},
            "dynamic_content": {},
            "social_media_deep_dive": {},
        }

        try:
            async with PlaywrightAutomation(headless=self.headless) as playwright:

                # Advanced page analysis
                if target.startswith(("http://", "https://")):
                    url = target
                else:
                    # Search for target and analyze top result
                    search_results = self.serper.search(target, search_type="search")
                    if search_results.get("organic"):
                        url = search_results["organic"][0]["link"]
                    else:
                        url = f"https://{target}"

                # Advanced scraping
                result["advanced_analysis"] = await playwright.scrape_page_advanced(url)

                # Dynamic content extraction
                dynamic_selectors = {
                    "headings": "h1, h2, h3",
                    "links": "a[href]",
                    "images": "img[src]",
                    "forms": "form",
                    "scripts": "script[src]",
                }

                result["dynamic_content"] = await playwright.extract_dynamic_content(
                    url, dynamic_selectors
                )

                # Social media deep dive
                social_platforms = ["twitter", "linkedin", "facebook"]
                for platform in social_platforms:
                    search_query = f"site:{platform}.com {target}"
                    search_results = self.serper.search(search_query, search_type="search")

                    if search_results.get("organic"):
                        profile_url = search_results["organic"][0]["link"]

                        result["social_media_deep_dive"][platform] = (
                            await playwright.perform_social_media_scraping(platform, profile_url)
                        )

        except Exception as e:
            result["error"] = str(e)
            osint_logger.log_error(e, "advanced_investigation")

        return result
