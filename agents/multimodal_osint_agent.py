"""
🧠 CrewAI OSINT Agent Framework - Multi-Modal OSINT Agent

Enhanced OSINT agent with multi-modal analysis capabilities for images and videos.
Combines traditional OSINT techniques with advanced image/video analysis.
"""

import base64
import json
import sys
import tempfile
from datetime import datetime
from io import Bytes<PERSON>
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import requests

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from agents.base_agent import BaseOSINTAgent
from tools.crawl4ai_wrapper import Crawl4AITool
from tools.multimodal_analyzer import MultiModalAnalyzer
from tools.serper_wrapper import SerperSearchTool
from utils.logging_config import log_execution_time, osint_logger
from utils.memory_manager import StatefulAgent


class MultiModalOSINTAgent(StatefulAgent):
    """OSINT agent with multi-modal analysis capabilities"""

    def __init__(self, user_id: str = "default"):
        super().__init__(agent_type="multimodal", user_id=user_id)

        self.multimodal_analyzer = MultiModalAnalyzer()
        self.serper = SerperSearchTool()
        self.crawler = Crawl4AITool()

        self.analysis_history = []

        osint_logger.logger.info("Multi-modal OSINT agent initialized", user_id=user_id)

    @log_execution_time("multimodal_osint")
    def analyze_media_with_context(
        self,
        media_input: Union[str, bytes, Path],
        media_type: str = "auto",
        analysis_types: List[str] = None,
        osint_context: bool = True,
    ) -> Dict[str, Any]:
        """Comprehensive media analysis with OSINT context"""

        # Start conversation if not already started
        if not self.conversation_id:
            initial_context = {
                "media_type": media_type,
                "analysis_types": analysis_types or [],
                "started_at": datetime.now().isoformat(),
            }
            self.start_conversation(initial_context)

        result = {
            "analysis_timestamp": datetime.now().isoformat(),
            "media_type": media_type,
            "multimodal_analysis": {},
            "osint_enrichment": {},
            "contextual_insights": {},
            "security_assessment": {},
            "conversation_context": {},
        }

        try:
            # Determine media type if auto
            if media_type == "auto":
                media_type = self._detect_media_type(media_input)
                result["media_type"] = media_type

            # Perform multi-modal analysis
            if media_type == "image":
                result["multimodal_analysis"] = self.multimodal_analyzer.analyze_image(
                    media_input, analysis_types
                )
            elif media_type == "video":
                result["multimodal_analysis"] = self.multimodal_analyzer.analyze_video(
                    media_input, analysis_types
                )
            else:
                raise ValueError(f"Unsupported media type: {media_type}")

            # OSINT enrichment
            if osint_context:
                result["osint_enrichment"] = self._perform_osint_enrichment(
                    result["multimodal_analysis"]
                )

            # Get conversation context
            context = self.get_conversation_context()
            result["conversation_context"] = {
                "conversation_id": self.conversation_id,
                "message_count": context.get("message_count", 0),
                "relevant_memories": len(context.get("relevant_memories", [])),
            }

            # Contextual insights
            result["contextual_insights"] = self._generate_contextual_insights(result, context)

            # Security assessment
            result["security_assessment"] = self._assess_security_implications(result)

            # Store analysis
            self.add_agent_response(
                content=f"Multi-modal analysis completed for {media_type}", analysis_data=result
            )

            # Update conversation context
            self.update_context(
                {
                    "last_analysis": {
                        "media_type": media_type,
                        "timestamp": datetime.now().isoformat(),
                        "faces_detected": result["multimodal_analysis"]
                        .get("face_analysis", {})
                        .get("face_count", 0),
                        "text_found": bool(
                            result["multimodal_analysis"]
                            .get("text_extraction", {})
                            .get("text", "")
                            .strip()
                        ),
                    }
                }
            )

            self.analysis_history.append(result)

            osint_logger.logger.info(
                "Multi-modal OSINT analysis completed",
                media_type=media_type,
                conversation_id=self.conversation_id,
                faces_detected=result["multimodal_analysis"]
                .get("face_analysis", {})
                .get("face_count", 0),
            )

        except Exception as e:
            osint_logger.log_error(e, "multimodal_osint_analysis")
            result["error"] = str(e)

        return result

    def _detect_media_type(self, media_input: Union[str, bytes, Path]) -> str:
        """Detect media type from input"""

        if isinstance(media_input, bytes):
            # Check magic bytes for common formats
            if media_input.startswith(b"\xff\xd8\xff"):
                return "image"  # JPEG
            elif media_input.startswith(b"\x89PNG"):
                return "image"  # PNG
            elif media_input.startswith(b"GIF"):
                return "image"  # GIF
            elif media_input.startswith(b"\x00\x00\x00\x18ftypmp4"):
                return "video"  # MP4
            else:
                return "image"  # Default to image

        elif isinstance(media_input, (str, Path)):
            path = Path(media_input)
            suffix = path.suffix.lower()

            image_formats = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp", ".gif"]
            video_formats = [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv"]

            if suffix in image_formats:
                return "image"
            elif suffix in video_formats:
                return "video"
            else:
                return "image"  # Default

        return "image"

    def _perform_osint_enrichment(self, multimodal_result: Dict[str, Any]) -> Dict[str, Any]:
        """Perform OSINT enrichment based on multi-modal analysis"""

        enrichment = {
            "text_based_searches": [],
            "reverse_image_searches": [],
            "geolocation_attempts": [],
            "metadata_analysis": {},
            "external_references": [],
        }

        try:
            # Text-based OSINT
            extracted_text = multimodal_result.get("text_extraction", {}).get("text", "")
            if extracted_text.strip():
                enrichment["text_based_searches"] = self._search_extracted_text(extracted_text)

            # Metadata analysis
            metadata = multimodal_result.get("metadata", {})
            if metadata:
                enrichment["metadata_analysis"] = self._analyze_metadata_for_osint(metadata)

            # Geolocation from EXIF
            exif_data = metadata.get("exif_data", {})
            if exif_data:
                enrichment["geolocation_attempts"] = self._attempt_geolocation(exif_data)

            # Security indicators analysis
            security_indicators = multimodal_result.get("security_indicators", [])
            if security_indicators:
                enrichment["security_analysis"] = self._analyze_security_indicators(
                    security_indicators
                )

        except Exception as e:
            enrichment["error"] = str(e)

        return enrichment

    def _search_extracted_text(self, text: str) -> List[Dict[str, Any]]:
        """Search for extracted text using OSINT sources"""

        searches = []

        try:
            # Split text into meaningful chunks
            text_chunks = [chunk.strip() for chunk in text.split("\n") if len(chunk.strip()) > 3]

            for chunk in text_chunks[:5]:  # Limit to first 5 chunks
                if len(chunk) > 100:
                    chunk = chunk[:100] + "..."

                try:
                    # Search using Serper
                    search_results = self.serper.search(chunk, search_type="search")

                    searches.append(
                        {
                            "query": chunk,
                            "source": "serper",
                            "results_count": len(search_results.get("organic", [])),
                            "top_results": search_results.get("organic", [])[:3],
                        }
                    )

                except Exception as e:
                    searches.append({"query": chunk, "source": "serper", "error": str(e)})

        except Exception as e:
            searches.append({"error": str(e)})

        return searches

    def _analyze_metadata_for_osint(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze metadata for OSINT purposes"""

        analysis = {
            "camera_info": {},
            "software_info": {},
            "temporal_info": {},
            "technical_indicators": {},
        }

        try:
            exif_data = metadata.get("exif_data", {})

            # Camera information
            camera_fields = ["Make", "Model", "LensModel", "LensSerialNumber"]
            for field in camera_fields:
                if field in exif_data:
                    analysis["camera_info"][field.lower()] = exif_data[field]

            # Software information
            software_fields = ["Software", "ProcessingSoftware", "HostComputer"]
            for field in software_fields:
                if field in exif_data:
                    analysis["software_info"][field.lower()] = exif_data[field]

            # Temporal information
            time_fields = ["DateTime", "DateTimeOriginal", "DateTimeDigitized"]
            for field in time_fields:
                if field in exif_data:
                    analysis["temporal_info"][field.lower()] = exif_data[field]

            # Technical indicators
            if "ImageWidth" in exif_data and "ImageLength" in exif_data:
                analysis["technical_indicators"][
                    "resolution"
                ] = f"{exif_data['ImageWidth']}x{exif_data['ImageLength']}"

            if "Orientation" in exif_data:
                analysis["technical_indicators"]["orientation"] = exif_data["Orientation"]

        except Exception as e:
            analysis["error"] = str(e)

        return analysis

    def _attempt_geolocation(self, exif_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Attempt to extract geolocation from EXIF data"""

        geolocation_attempts = []

        try:
            # Look for GPS coordinates
            gps_fields = ["GPSLatitude", "GPSLongitude", "GPSLatitudeRef", "GPSLongitudeRef"]
            gps_data = {}

            for field in gps_fields:
                if field in exif_data:
                    gps_data[field] = exif_data[field]

            if gps_data:
                geolocation_attempts.append(
                    {
                        "method": "exif_gps",
                        "data": gps_data,
                        "confidence": "high" if len(gps_data) >= 4 else "medium",
                    }
                )

            # Look for location names in other fields
            location_fields = ["LocationName", "City", "State", "Country"]
            for field in location_fields:
                if field in exif_data:
                    geolocation_attempts.append(
                        {
                            "method": "exif_location_name",
                            "field": field,
                            "value": exif_data[field],
                            "confidence": "medium",
                        }
                    )

        except Exception as e:
            geolocation_attempts.append({"error": str(e)})

        return geolocation_attempts

    def _analyze_security_indicators(self, indicators: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze security indicators for OSINT purposes"""

        analysis = {"risk_level": "low", "concerns": [], "recommendations": []}

        try:
            high_risk_count = 0
            medium_risk_count = 0

            for indicator in indicators:
                confidence = indicator.get("confidence", 0)
                indicator_type = indicator.get("type", "")

                if confidence > 0.7:
                    high_risk_count += 1
                    analysis["concerns"].append(
                        {
                            "type": indicator_type,
                            "description": indicator.get("description", ""),
                            "severity": "high",
                        }
                    )
                elif confidence > 0.4:
                    medium_risk_count += 1
                    analysis["concerns"].append(
                        {
                            "type": indicator_type,
                            "description": indicator.get("description", ""),
                            "severity": "medium",
                        }
                    )

            # Determine overall risk level
            if high_risk_count > 0:
                analysis["risk_level"] = "high"
            elif medium_risk_count > 2:
                analysis["risk_level"] = "medium"

            # Generate recommendations
            if analysis["risk_level"] == "high":
                analysis["recommendations"].append("Conduct thorough investigation of image source")
                analysis["recommendations"].append("Verify authenticity through multiple sources")
            elif analysis["risk_level"] == "medium":
                analysis["recommendations"].append("Additional verification recommended")

        except Exception as e:
            analysis["error"] = str(e)

        return analysis

    def _generate_contextual_insights(
        self, result: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate insights based on conversation context"""

        insights = {
            "conversation_patterns": {},
            "cross_analysis_correlations": {},
            "temporal_analysis": {},
        }

        try:
            # Analyze conversation patterns
            recent_messages = context.get("recent_messages", [])
            if len(recent_messages) > 1:
                insights["conversation_patterns"] = {
                    "message_count": len(recent_messages),
                    "analysis_focus": "multi_modal_investigation",
                    "user_intent": "media_analysis",
                }

            # Look for correlations with previous analyses
            relevant_memories = context.get("relevant_memories", [])
            correlations = []

            for memory in relevant_memories:
                if memory.get("content_type") == "analysis":
                    try:
                        analysis_data = json.loads(memory.get("content", "{}"))
                        if "multimodal_analysis" in analysis_data:
                            correlations.append(
                                {
                                    "timestamp": memory.get("timestamp"),
                                    "similarity": "media_analysis",
                                    "previous_findings": analysis_data.get(
                                        "multimodal_analysis", {}
                                    )
                                    .get("text_extraction", {})
                                    .get("text", "")[:100],
                                }
                            )
                    except:
                        continue

            insights["cross_analysis_correlations"] = correlations

        except Exception as e:
            insights["error"] = str(e)

        return insights

    def _assess_security_implications(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Assess security implications of the analysis"""

        assessment = {
            "overall_risk": "low",
            "privacy_concerns": [],
            "authenticity_indicators": {},
            "recommendations": [],
        }

        try:
            # Check for privacy concerns
            face_count = result["multimodal_analysis"].get("face_analysis", {}).get("face_count", 0)
            if face_count > 0:
                assessment["privacy_concerns"].append(
                    {
                        "type": "facial_data",
                        "count": face_count,
                        "concern": "Biometric data present",
                    }
                )

            # Check extracted text for sensitive information
            extracted_text = (
                result["multimodal_analysis"].get("text_extraction", {}).get("text", "")
            )
            sensitive_patterns = ["password", "ssn", "credit card", "phone", "email"]

            for pattern in sensitive_patterns:
                if pattern.lower() in extracted_text.lower():
                    assessment["privacy_concerns"].append(
                        {
                            "type": "sensitive_text",
                            "pattern": pattern,
                            "concern": "Potentially sensitive information detected",
                        }
                    )

            # Authenticity indicators
            forensic_analysis = result["multimodal_analysis"].get("forensic_analysis", {})
            if forensic_analysis:
                noise_level = forensic_analysis.get("noise_analysis", {}).get("noise_level", 0)
                assessment["authenticity_indicators"] = {
                    "noise_level": noise_level,
                    "quality_assessment": forensic_analysis.get("noise_analysis", {}).get(
                        "quality_assessment", "unknown"
                    ),
                    "modification_likelihood": "high" if noise_level > 1000 else "low",
                }

            # Overall risk assessment
            if len(assessment["privacy_concerns"]) > 2:
                assessment["overall_risk"] = "high"
            elif len(assessment["privacy_concerns"]) > 0:
                assessment["overall_risk"] = "medium"

            # Generate recommendations
            if assessment["overall_risk"] == "high":
                assessment["recommendations"].extend(
                    [
                        "Handle with strict privacy protocols",
                        "Verify source authenticity",
                        "Consider legal implications",
                    ]
                )
            elif assessment["overall_risk"] == "medium":
                assessment["recommendations"].append("Exercise caution with sensitive data")

        except Exception as e:
            assessment["error"] = str(e)

        return assessment

    def batch_analyze_media(
        self, media_files: List[Union[str, Path]], analysis_types: List[str] = None
    ) -> Dict[str, Any]:
        """Analyze multiple media files in batch"""

        batch_result = {
            "batch_info": {
                "total_files": len(media_files),
                "analysis_types": analysis_types,
                "timestamp": datetime.now().isoformat(),
            },
            "results": [],
            "summary": {},
        }

        successful_analyses = 0
        total_faces = 0
        total_text_extractions = 0
        security_concerns = 0

        for i, media_file in enumerate(media_files):
            try:
                result = self.analyze_media_with_context(
                    media_file, analysis_types=analysis_types, osint_context=True
                )

                result["batch_index"] = i
                batch_result["results"].append(result)

                if "error" not in result:
                    successful_analyses += 1

                    # Aggregate statistics
                    face_count = (
                        result["multimodal_analysis"].get("face_analysis", {}).get("face_count", 0)
                    )
                    total_faces += face_count

                    if (
                        result["multimodal_analysis"]
                        .get("text_extraction", {})
                        .get("text", "")
                        .strip()
                    ):
                        total_text_extractions += 1

                    if result["security_assessment"].get("overall_risk") in ["medium", "high"]:
                        security_concerns += 1

            except Exception as e:
                batch_result["results"].append(
                    {"batch_index": i, "media_file": str(media_file), "error": str(e)}
                )

        # Generate batch summary
        batch_result["summary"] = {
            "successful_analyses": successful_analyses,
            "failed_analyses": len(media_files) - successful_analyses,
            "total_faces_detected": total_faces,
            "files_with_text": total_text_extractions,
            "security_concerns": security_concerns,
            "success_rate": successful_analyses / len(media_files) if media_files else 0,
        }

        return batch_result
