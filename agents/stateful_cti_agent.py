"""
🧠 CrewAI OSINT Agent Framework - Stateful CTI Agent

Enhanced cyber threat intelligence agent with memory persistence and
stateful conversations using LangGraph integration for context-aware analysis.
"""

import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from agents.cti_agent import CTIAgent
from utils.logging_config import log_execution_time, osint_logger
from utils.memory_manager import StatefulAgent, memory_manager


class StatefulCTIAgent(StatefulAgent):
    """CTI agent with memory and conversation state"""

    def __init__(self, user_id: str = "default"):
        super().__init__(agent_type="cti", user_id=user_id)
        self.base_agent = CTIAgent()
        self.ioc_database = {}  # Local IOC cache
        self.threat_actor_profiles = {}  # Local threat actor cache

        osint_logger.logger.info(f"Stateful CTI agent initialized for user: {user_id}")

    @log_execution_time("stateful_cti_agent")
    def analyze_threat_with_context(
        self, content: str, analysis_type: str = "ioc_extraction"
    ) -> Dict[str, Any]:
        """Perform threat analysis with conversation context"""

        # Start conversation if not already started
        if not self.conversation_id:
            initial_context = {
                "analysis_type": analysis_type,
                "started_at": datetime.now().isoformat(),
                "threat_focus": "general",
            }
            self.start_conversation(initial_context)

        # Add content to conversation
        self.add_user_message(
            content=content[:500],  # Truncate for storage
            metadata={"analysis_type": analysis_type, "content_length": len(content)},
        )

        # Get conversation context
        context = self.get_conversation_context()

        # Perform contextual analysis
        try:
            if analysis_type == "ioc_extraction":
                result = self._extract_iocs_with_context(content, context)
            elif analysis_type == "threat_actor_tracking":
                result = self._track_threat_actor_with_context(content, context)
            elif analysis_type == "campaign_analysis":
                result = self._analyze_campaign_with_context(content, context)
            elif analysis_type == "malware_analysis":
                result = self._analyze_malware_with_context(content, context)
            else:
                result = self._extract_iocs_with_context(content, context)

            # Add conversation metadata
            result["conversation_context"] = {
                "conversation_id": self.conversation_id,
                "message_count": context.get("message_count", 0),
                "relevant_memories": len(context.get("relevant_memories", [])),
                "analysis_type": analysis_type,
            }

            # Store analysis result
            self.add_agent_response(
                content=f"CTI analysis completed: {analysis_type}", analysis_data=result
            )

            # Update conversation context
            self.update_context(
                {
                    "last_analysis": {
                        "type": analysis_type,
                        "ioc_count": len(result.get("iocs", [])),
                        "threat_actors": result.get("threat_actors", []),
                        "timestamp": datetime.now().isoformat(),
                    }
                }
            )

            # Update local caches
            self._update_local_caches(result)

            osint_logger.logger.info(
                "Contextual CTI analysis completed",
                analysis_type=analysis_type,
                ioc_count=len(result.get("iocs", [])),
                conversation_id=self.conversation_id,
            )

            return result

        except Exception as e:
            osint_logger.log_error(
                e,
                "stateful_cti_analysis",
                {"analysis_type": analysis_type, "conversation_id": self.conversation_id},
            )

            error_result = {
                "error": str(e),
                "analysis_type": analysis_type,
                "timestamp": datetime.now().isoformat(),
            }

            self.add_agent_response(
                content=f"CTI analysis failed: {str(e)}", analysis_data=error_result
            )

            return error_result

    def _extract_iocs_with_context(self, content: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract IOCs with conversation context"""

        # Get base IOC extraction
        base_iocs = self.base_agent.extract_iocs(content)

        # Enhance with historical context
        enhanced_result = {
            "iocs": base_iocs,
            "contextual_analysis": {},
            "historical_matches": [],
            "confidence_scores": {},
        }

        # Check against previous IOCs in conversation
        previous_iocs = self._get_previous_iocs_from_context(context)

        for ioc in base_iocs:
            ioc_value = ioc.get("value", "")

            # Check for historical matches
            if ioc_value in previous_iocs:
                enhanced_result["historical_matches"].append(
                    {
                        "ioc": ioc_value,
                        "previous_occurrence": previous_iocs[ioc_value],
                        "pattern": "recurring_indicator",
                    }
                )

            # Calculate confidence based on context
            confidence = self._calculate_ioc_confidence(ioc, context)
            enhanced_result["confidence_scores"][ioc_value] = confidence

            # Update local IOC database
            self.ioc_database[ioc_value] = {
                "ioc": ioc,
                "first_seen": datetime.now().isoformat(),
                "conversation_id": self.conversation_id,
                "confidence": confidence,
            }

        # Add contextual insights
        if enhanced_result["historical_matches"]:
            enhanced_result["contextual_analysis"]["recurring_patterns"] = True
            enhanced_result["contextual_analysis"]["pattern_count"] = len(
                enhanced_result["historical_matches"]
            )

        return enhanced_result

    def _track_threat_actor_with_context(
        self, content: str, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Track threat actors with conversation context"""

        # Extract potential threat actor names (simplified)
        potential_actors = self._extract_threat_actor_names(content)

        result = {
            "potential_threat_actors": potential_actors,
            "known_actors": [],
            "contextual_connections": [],
            "confidence_assessment": {},
        }

        # Check against known threat actors
        for actor_name in potential_actors:
            if actor_name in self.base_agent.threat_actor_profiles:
                profile = self.base_agent.threat_actor_profiles[actor_name]
                result["known_actors"].append(
                    {
                        "name": actor_name,
                        "profile": profile,
                        "context_relevance": self._assess_actor_relevance(actor_name, context),
                    }
                )

        # Look for connections in conversation history
        previous_actors = self._get_previous_actors_from_context(context)
        for actor in potential_actors:
            if actor in previous_actors:
                result["contextual_connections"].append(
                    {
                        "actor": actor,
                        "previous_mention": previous_actors[actor],
                        "connection_type": "conversation_recurring",
                    }
                )

        return result

    def _analyze_campaign_with_context(
        self, content: str, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze campaigns with conversation context"""

        # Get IOCs first
        iocs = self.base_agent.extract_iocs(content)

        # Correlate with conversation history
        campaign_indicators = []

        # Look for IOC patterns across conversation
        previous_iocs = self._get_previous_iocs_from_context(context)

        # Simple campaign correlation based on IOC overlap
        overlapping_iocs = []
        for ioc in iocs:
            ioc_value = ioc.get("value", "")
            if ioc_value in previous_iocs:
                overlapping_iocs.append(ioc_value)

        result = {
            "campaign_analysis": {
                "ioc_overlap": len(overlapping_iocs),
                "overlapping_indicators": overlapping_iocs,
                "campaign_likelihood": (
                    "high"
                    if len(overlapping_iocs) > 2
                    else "medium" if len(overlapping_iocs) > 0 else "low"
                ),
            },
            "temporal_analysis": {
                "conversation_span": self._calculate_conversation_timespan(context),
                "analysis_frequency": context.get("message_count", 0),
            },
            "correlation_data": self.base_agent.correlate_campaign_data(
                content, ["IOC Correlation"]
            ),
        }

        return result

    def _analyze_malware_with_context(
        self, content: str, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze malware with conversation context"""

        # Extract IOCs and look for malware indicators
        iocs = self.base_agent.extract_iocs(content)

        # Look for malware-related keywords
        malware_keywords = ["malware", "trojan", "ransomware", "backdoor", "rat", "apt", "payload"]
        malware_indicators = [
            keyword for keyword in malware_keywords if keyword.lower() in content.lower()
        ]

        result = {
            "malware_indicators": malware_indicators,
            "file_hashes": [ioc for ioc in iocs if ioc.get("type") in ["md5", "sha1", "sha256"]],
            "network_indicators": [
                ioc for ioc in iocs if ioc.get("type") in ["ip", "domain", "url"]
            ],
            "contextual_analysis": {
                "malware_focus": len(malware_indicators) > 0,
                "hash_count": len(
                    [ioc for ioc in iocs if ioc.get("type") in ["md5", "sha1", "sha256"]]
                ),
                "network_count": len(
                    [ioc for ioc in iocs if ioc.get("type") in ["ip", "domain", "url"]]
                ),
            },
        }

        # Check for malware family patterns in conversation
        previous_analyses = self._get_previous_analyses_from_context(context)
        malware_families = set()

        for analysis in previous_analyses:
            if "malware_indicators" in analysis:
                malware_families.update(analysis["malware_indicators"])

        if malware_families:
            result["contextual_analysis"]["recurring_families"] = list(malware_families)

        return result

    def _get_previous_iocs_from_context(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Extract IOCs from conversation context"""

        previous_iocs = {}

        for memory in context.get("relevant_memories", []):
            if memory.get("content_type") == "analysis":
                try:
                    analysis_data = json.loads(memory.get("content", "{}"))
                    if "iocs" in analysis_data:
                        for ioc in analysis_data["iocs"]:
                            ioc_value = ioc.get("value", "")
                            if ioc_value:
                                previous_iocs[ioc_value] = memory.get("timestamp", "")
                except:
                    continue

        return previous_iocs

    def _get_previous_actors_from_context(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Extract threat actors from conversation context"""

        previous_actors = {}

        for memory in context.get("relevant_memories", []):
            if memory.get("content_type") == "analysis":
                try:
                    analysis_data = json.loads(memory.get("content", "{}"))
                    if "potential_threat_actors" in analysis_data:
                        for actor in analysis_data["potential_threat_actors"]:
                            previous_actors[actor] = memory.get("timestamp", "")
                except:
                    continue

        return previous_actors

    def _get_previous_analyses_from_context(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get previous analysis data from context"""

        analyses = []

        for memory in context.get("relevant_memories", []):
            if memory.get("content_type") == "analysis":
                try:
                    analysis_data = json.loads(memory.get("content", "{}"))
                    analyses.append(analysis_data)
                except:
                    continue

        return analyses

    def _calculate_ioc_confidence(self, ioc: Dict[str, Any], context: Dict[str, Any]) -> float:
        """Calculate IOC confidence based on context"""

        base_confidence = 0.5

        # Increase confidence if IOC appeared before
        ioc_value = ioc.get("value", "")
        previous_iocs = self._get_previous_iocs_from_context(context)

        if ioc_value in previous_iocs:
            base_confidence += 0.3

        # Increase confidence based on IOC type
        ioc_type = ioc.get("type", "")
        if ioc_type in ["md5", "sha1", "sha256"]:
            base_confidence += 0.2
        elif ioc_type in ["ip", "domain"]:
            base_confidence += 0.1

        return min(base_confidence, 1.0)

    def _extract_threat_actor_names(self, content: str) -> List[str]:
        """Extract potential threat actor names from content"""

        # Simple extraction based on known patterns
        known_actors = list(self.base_agent.threat_actor_profiles.keys())
        found_actors = []

        content_lower = content.lower()
        for actor in known_actors:
            if actor.lower() in content_lower:
                found_actors.append(actor)

        return found_actors

    def _assess_actor_relevance(self, actor_name: str, context: Dict[str, Any]) -> float:
        """Assess threat actor relevance to current context"""

        relevance = 0.5

        # Check if actor was mentioned before
        previous_actors = self._get_previous_actors_from_context(context)
        if actor_name in previous_actors:
            relevance += 0.3

        return min(relevance, 1.0)

    def _calculate_conversation_timespan(self, context: Dict[str, Any]) -> str:
        """Calculate timespan of conversation"""

        messages = context.get("recent_messages", [])
        if len(messages) < 2:
            return "single_message"

        try:
            first_msg = datetime.fromisoformat(messages[0].get("timestamp", ""))
            last_msg = datetime.fromisoformat(messages[-1].get("timestamp", ""))

            duration = last_msg - first_msg

            if duration.days > 0:
                return f"{duration.days}_days"
            elif duration.seconds > 3600:
                return f"{duration.seconds // 3600}_hours"
            else:
                return f"{duration.seconds // 60}_minutes"
        except:
            return "unknown"

    def _update_local_caches(self, result: Dict[str, Any]):
        """Update local IOC and threat actor caches"""

        # Update IOC cache
        if "iocs" in result:
            for ioc in result["iocs"]:
                ioc_value = ioc.get("value", "")
                if ioc_value:
                    self.ioc_database[ioc_value] = {
                        "ioc": ioc,
                        "conversation_id": self.conversation_id,
                        "timestamp": datetime.now().isoformat(),
                    }

        # Update threat actor cache
        if "potential_threat_actors" in result:
            for actor in result["potential_threat_actors"]:
                self.threat_actor_profiles[actor] = {
                    "name": actor,
                    "conversation_id": self.conversation_id,
                    "first_seen": datetime.now().isoformat(),
                }

    def get_ioc_summary(self) -> Dict[str, Any]:
        """Get summary of IOCs from conversation"""

        if not self.conversation_id:
            return {"error": "No active conversation"}

        # Get all IOCs from conversation memories
        memories = memory_manager.get_relevant_memories(
            self.conversation_id, content_type="analysis", limit=100
        )

        all_iocs = []
        ioc_types = {}

        for memory in memories:
            try:
                analysis_data = json.loads(memory.content)
                if "iocs" in analysis_data:
                    for ioc in analysis_data["iocs"]:
                        all_iocs.append(ioc)
                        ioc_type = ioc.get("type", "unknown")
                        ioc_types[ioc_type] = ioc_types.get(ioc_type, 0) + 1
            except:
                continue

        return {
            "conversation_id": self.conversation_id,
            "total_iocs": len(all_iocs),
            "unique_iocs": len(set(ioc.get("value", "") for ioc in all_iocs)),
            "ioc_types": ioc_types,
            "recent_iocs": all_iocs[-10:] if all_iocs else [],
        }
