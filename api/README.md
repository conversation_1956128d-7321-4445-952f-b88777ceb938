# 🧠 OSINT Framework - REST API

FastAPI-based REST API for the CrewAI OSINT Agent Framework, providing programmatic access to all agent capabilities.

## 🚀 Quick Start

### Launch the API
```bash
# From project root
python run_api.py

# Or directly with uvicorn
uvicorn api.main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at:
- **API Base**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 📋 API Endpoints

### 🏠 General Endpoints

#### Health Check
```http
GET /health
```
Returns API health status and configuration.

#### Root
```http
GET /
```
Returns basic API information.

### 🌍 Geopolitical Analysis

#### Start Geopolitical Analysis
```http
POST /api/v1/geopolitical/analyze
Content-Type: application/json

{
  "topic": "Middle East tensions",
  "regions": ["Middle East", "Eastern Europe"],
  "time_range": "30d",
  "analysis_type": "intelligence_brief"
}
```

#### Generate Situation Report
```http
POST /api/v1/geopolitical/situation-report?time_range=24h
Content-Type: application/json

["Asia", "Europe", "Americas"]
```

### 🔒 Cyber Threat Intelligence

#### Start CTI Analysis
```http
POST /api/v1/cti/analyze
Content-Type: application/json

{
  "text_content": "Threat report content...",
  "analysis_type": "ioc_extraction",
  "context": "Optional context"
}
```

#### Extract IOCs (Synchronous)
```http
POST /api/v1/cti/extract-iocs
Content-Type: application/json

{
  "text_content": "IP: ***********, Domain: malicious.com"
}
```

### 📚 Document Management

#### Index Documents
```http
POST /api/v1/documents/index
Content-Type: application/json

{
  "documents": [
    {
      "content": "Document content...",
      "metadata": {"title": "Report 1"}
    }
  ],
  "urls": ["https://example.com/report"],
  "text_content": "Additional text to index"
}
```

#### Search Documents
```http
POST /api/v1/documents/search
Content-Type: application/json

{
  "query": "threat intelligence",
  "search_type": "semantic",
  "max_results": 5,
  "filters": {}
}
```

### 📋 Task Management

#### Get Task Status
```http
GET /api/v1/tasks/{task_id}
```

#### List Tasks
```http
GET /api/v1/tasks?limit=10&status=completed
```

### 📤 File Upload

#### Upload and Analyze File
```http
POST /api/v1/upload/analyze
Content-Type: multipart/form-data

file: [binary file data]
analysis_type: auto|cti|geopolitical
```

## 🔧 Request/Response Models

### GeopoliticalAnalysisRequest
```json
{
  "topic": "string",
  "regions": ["string"],
  "time_range": "string",
  "analysis_type": "string"
}
```

### CTIAnalysisRequest
```json
{
  "text_content": "string",
  "analysis_type": "string",
  "context": "string"
}
```

### AnalysisResponse
```json
{
  "task_id": "string",
  "status": "pending|running|completed|failed",
  "result": {},
  "error": "string",
  "timestamp": "string"
}
```

## 🔄 Asynchronous Processing

Most analysis endpoints use background tasks for processing:

1. **Submit Request**: POST to analysis endpoint
2. **Get Task ID**: Receive task ID in response
3. **Poll Status**: GET `/api/v1/tasks/{task_id}` to check progress
4. **Retrieve Results**: Results available when status is "completed"

### Example Workflow
```python
import requests
import time

# 1. Start analysis
response = requests.post("http://localhost:8000/api/v1/cti/analyze", json={
    "text_content": "Threat report...",
    "analysis_type": "ioc_extraction"
})
task_id = response.json()["task_id"]

# 2. Poll for completion
while True:
    status_response = requests.get(f"http://localhost:8000/api/v1/tasks/{task_id}")
    status = status_response.json()["status"]
    
    if status == "completed":
        result = status_response.json()["result"]
        break
    elif status == "failed":
        error = status_response.json()["error"]
        print(f"Analysis failed: {error}")
        break
    
    time.sleep(2)  # Wait 2 seconds before checking again
```

## 🔑 Authentication

Currently, the API uses API key validation through environment variables:
- `OPENAI_API_KEY`: Required for LLM operations
- `SERPER_API_KEY`: Required for web search

Future versions will include:
- JWT token authentication
- API key management
- Rate limiting
- User permissions

## 📊 Response Codes

- **200**: Success
- **400**: Bad Request (missing API keys, invalid parameters)
- **404**: Not Found (task not found)
- **500**: Internal Server Error

## 🔧 Configuration

### Environment Variables
```bash
OPENAI_API_KEY=your_openai_api_key
SERPER_API_KEY=your_serper_api_key
```

### CORS Settings
The API includes CORS middleware configured for development. For production:
- Restrict `allow_origins` to specific domains
- Configure appropriate security headers
- Enable authentication

## 🧪 Testing

### Manual Testing
Use the interactive docs at `/docs` to test endpoints directly in your browser.

### Python Client Example
```python
import requests

# Health check
response = requests.get("http://localhost:8000/health")
print(response.json())

# Extract IOCs
response = requests.post(
    "http://localhost:8000/api/v1/cti/extract-iocs",
    json={"text_content": "Malicious IP: ***********"}
)
print(response.json())
```

### cURL Examples
```bash
# Health check
curl http://localhost:8000/health

# Extract IOCs
curl -X POST http://localhost:8000/api/v1/cti/extract-iocs \
  -H "Content-Type: application/json" \
  -d '{"text_content": "Suspicious domain: evil.com"}'
```

## 🚀 Deployment

### Development
```bash
python run_api.py
```

### Production
```bash
# With Gunicorn
pip install gunicorn
gunicorn api.main:app -w 4 -k uvicorn.workers.UvicornWorker

# With Docker (future)
docker build -t osint-api .
docker run -p 8000:8000 osint-api
```

## 📈 Monitoring

Future enhancements will include:
- Request/response logging
- Performance metrics
- Error tracking
- Usage analytics

## 🔒 Security Considerations

- API keys are validated but not stored
- File uploads are processed in memory
- No persistent storage of sensitive data
- CORS configured for development (restrict for production)

---

**📅 Last Updated**: June 18, 2025  
**🔄 Status**: Fully functional with all core endpoints implemented
