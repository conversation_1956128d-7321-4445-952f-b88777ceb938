# 🧠 CrewAI OSINT Agent Framework - Monitoring Configuration

# Logging Configuration
logging:
  level: INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # File logging
  file_logging:
    enabled: true
    directory: "logs"
    max_file_size: "100MB"
    backup_count: 10
    rotation: "daily"
  
  # Structured logging
  structured_logging:
    enabled: true
    format: "json"
  
  # Module-specific logging levels
  modules:
    agents: INFO
    workflows: INFO
    rag: INFO
    api: INFO
    tools: DEBUG

# Metrics Configuration
metrics:
  collection_interval: 60  # seconds
  retention_period: 7  # days
  
  # Prometheus metrics
  prometheus:
    enabled: true
    port: 9090
    path: "/metrics"
  
  # System metrics
  system_metrics:
    enabled: true
    collect_cpu: true
    collect_memory: true
    collect_disk: true
    collect_network: true
    collect_processes: true
  
  # Application metrics
  application_metrics:
    enabled: true
    collect_request_metrics: true
    collect_task_metrics: true
    collect_error_metrics: true
    collect_performance_metrics: true

# Health Checks Configuration
health_checks:
  interval: 30  # seconds
  timeout: 10  # seconds
  
  # Built-in checks
  checks:
    api_keys:
      enabled: true
      critical: true
      description: "Check if required API keys are configured"
    
    disk_space:
      enabled: true
      critical: true
      threshold: 90  # percent
      description: "Check available disk space"
    
    memory_usage:
      enabled: true
      critical: false
      threshold: 85  # percent
      description: "Check memory usage"
    
    database_connection:
      enabled: false  # Enable when database is configured
      critical: true
      timeout: 5
      description: "Check database connectivity"
    
    external_apis:
      enabled: true
      critical: false
      apis:
        - name: "OpenAI"
          url: "https://api.openai.com/v1/models"
          timeout: 10
        - name: "Serper"
          url: "https://google.serper.dev"
          timeout: 10

# Alerting Configuration
alerting:
  enabled: true
  
  # Alert rules
  rules:
    high_cpu:
      enabled: true
      condition: "cpu_percent > 80"
      severity: "warning"
      cooldown_minutes: 5
      description: "High CPU usage detected"
    
    high_memory:
      enabled: true
      condition: "memory_percent > 85"
      severity: "critical"
      cooldown_minutes: 5
      description: "High memory usage detected"
    
    disk_space_low:
      enabled: true
      condition: "disk_percent > 90"
      severity: "critical"
      cooldown_minutes: 15
      description: "Low disk space detected"
    
    slow_response:
      enabled: true
      condition: "avg_response_time > 5.0"
      severity: "warning"
      cooldown_minutes: 10
      description: "Slow response times detected"
    
    high_error_rate:
      enabled: true
      condition: "error_rate > 0.05"  # 5%
      severity: "warning"
      cooldown_minutes: 5
      description: "High error rate detected"
    
    api_key_missing:
      enabled: true
      condition: "not api_keys_configured"
      severity: "critical"
      cooldown_minutes: 60
      description: "Required API keys not configured"
  
  # Notification channels
  notifications:
    console:
      enabled: true
      level: "warning"
    
    file:
      enabled: true
      file_path: "logs/alerts.log"
      level: "warning"
    
    webhook:
      enabled: false
      url: ""
      headers: {}
      level: "critical"
    
    email:
      enabled: false
      smtp_server: ""
      smtp_port: 587
      username: ""
      password: ""
      from_email: ""
      to_emails: []
      level: "critical"

# Performance Monitoring
performance:
  enabled: true
  
  # Request tracking
  request_tracking:
    enabled: true
    sample_rate: 1.0  # 100% sampling
    max_history: 1000
  
  # Resource monitoring
  resource_monitoring:
    enabled: true
    thresholds:
      cpu_warning: 70
      cpu_critical: 85
      memory_warning: 75
      memory_critical: 90
      disk_warning: 80
      disk_critical: 95
  
  # Performance profiling
  profiling:
    enabled: false  # Enable for debugging
    profile_requests: false
    profile_functions: false
    output_directory: "logs/profiles"

# Dashboard Configuration
dashboard:
  enabled: true
  refresh_interval: 30  # seconds
  
  # Widgets
  widgets:
    system_overview:
      enabled: true
      position: [0, 0]
      size: [6, 4]
    
    performance_metrics:
      enabled: true
      position: [6, 0]
      size: [6, 4]
    
    active_alerts:
      enabled: true
      position: [0, 4]
      size: [12, 3]
    
    recent_logs:
      enabled: true
      position: [0, 7]
      size: [12, 5]

# Data Retention
retention:
  metrics: 30  # days
  logs: 14  # days
  alerts: 90  # days
  performance_data: 7  # days
  
  # Cleanup schedule
  cleanup:
    enabled: true
    schedule: "0 2 * * *"  # Daily at 2 AM
    
# Security
security:
  # Monitoring endpoint security
  monitoring_endpoints:
    authentication_required: false
    allowed_ips: []  # Empty means all IPs allowed
    rate_limiting:
      enabled: true
      requests_per_minute: 60
  
  # Log security
  log_security:
    mask_sensitive_data: true
    sensitive_fields:
      - "api_key"
      - "password"
      - "token"
      - "secret"
    
  # Metrics security
  metrics_security:
    expose_system_info: true
    expose_application_info: true
    mask_sensitive_labels: true

# Integration
integrations:
  # Grafana
  grafana:
    enabled: false
    url: ""
    api_key: ""
    dashboard_id: ""
  
  # Elasticsearch
  elasticsearch:
    enabled: false
    hosts: []
    index_pattern: "osint-logs-*"
    username: ""
    password: ""
  
  # Datadog
  datadog:
    enabled: false
    api_key: ""
    app_key: ""
    tags: []
  
  # New Relic
  newrelic:
    enabled: false
    license_key: ""
    app_name: "OSINT Framework"
