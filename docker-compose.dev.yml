version: '3.8'

services:
  # Development OSINT Framework
  osint-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: osint-dev
    ports:
      - "8000:8000"  # API
      - "8501:8501"  # Streamlit UI
      - "8888:8888"  # Jupyter
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SERPER_API_KEY=${SERPER_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - GROQ_API_KEY=${GROQ_API_KEY:-}
      - HEADLESS_BROWSER=true
      - BROWSER_TIMEOUT=30
      - VECTOR_DB_PATH=/app/data/vector_index
      - LOG_LEVEL=DEBUG
      - DEBUG_MODE=true
    volumes:
      - .:/app  # Mount entire project for live development
      - /app/venv  # Exclude virtual environment
    networks:
      - osint-dev-network
    stdin_open: true
    tty: true
    restart: unless-stopped

  # Redis for development
  redis-dev:
    image: redis:7-alpine
    container_name: osint-redis-dev
    ports:
      - "6379:6379"
    networks:
      - osint-dev-network
    restart: unless-stopped

  # PostgreSQL for development
  postgres-dev:
    image: postgres:15-alpine
    container_name: osint-postgres-dev
    environment:
      - POSTGRES_DB=osint_framework_dev
      - POSTGRES_USER=osint
      - POSTGRES_PASSWORD=osint_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
    networks:
      - osint-dev-network
    restart: unless-stopped

volumes:
  postgres-dev-data:

networks:
  osint-dev-network:
    driver: bridge
