version: '3.8'

services:
  # Main OSINT Framework API
  osint-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: osint-api
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SERPER_API_KEY=${SERPER_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - GROQ_API_KEY=${GROQ_API_KEY:-}
      - HEADLESS_BROWSER=true
      - BROWSER_TIMEOUT=30
      - VECTOR_DB_PATH=/app/data/vector_index
      - LOG_LEVEL=INFO
      - DEBUG_MODE=false
    volumes:
      - ./data:/app/data
      - ./output:/app/output
      - ./logs:/app/logs
    networks:
      - osint-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Streamlit UI
  osint-ui:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: osint-ui
    ports:
      - "8501:8501"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SERPER_API_KEY=${SERPER_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - GROQ_API_KEY=${GROQ_API_KEY:-}
      - HEADLESS_BROWSER=true
      - BROWSER_TIMEOUT=30
      - VECTOR_DB_PATH=/app/data/vector_index
      - LOG_LEVEL=INFO
      - DEBUG_MODE=false
    volumes:
      - ./data:/app/data
      - ./output:/app/output
      - ./logs:/app/logs
    networks:
      - osint-network
    restart: unless-stopped
    command: ["python", "run_ui.py"]
    depends_on:
      - osint-api

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: osint-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - osint-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL for persistent data storage
  postgres:
    image: postgres:15-alpine
    container_name: osint-postgres
    environment:
      - POSTGRES_DB=osint_framework
      - POSTGRES_USER=osint
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-osint_secure_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - osint-network
    restart: unless-stopped

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: osint-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - osint-network
    restart: unless-stopped
    depends_on:
      - osint-api
      - osint-ui

volumes:
  redis-data:
  postgres-data:

networks:
  osint-network:
    driver: bridge
