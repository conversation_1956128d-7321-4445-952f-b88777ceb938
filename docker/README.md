# 🐳 Docker Deployment Guide

This directory contains Docker configuration files for deploying the CrewAI OSINT Agent Framework.

## 📋 Quick Start

### Prerequisites

- Docker 20.10+ installed
- Docker Compose 2.0+ installed
- At least 4GB RAM available
- API keys for OpenAI and Serper.dev

### 1. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit .env file and add your API keys
nano .env
```

### 2. Deploy Production Environment

```bash
# Build and deploy
./docker/deploy.sh deploy

# Check status
./docker/deploy.sh status
```

### 3. Access Services

- **API**: http://localhost:8000
- **UI**: http://localhost:8501
- **API Documentation**: http://localhost:8000/docs

## 🏗️ Architecture

### Production Stack

```
┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │   Streamlit UI  │
│  (Reverse Proxy)│────│   (Port 8501)   │
│   (Port 80)     │    └─────────────────┘
└─────────────────┘              │
         │                       │
         │              ┌─────────────────┐
         └──────────────│   FastAPI       │
                        │   (Port 8000)   │
                        └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   (Port 5432)   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │     Redis       │
                    │   (Port 6379)   │
                    └─────────────────┘
```

### Services

| Service | Description | Port | Health Check |
|---------|-------------|------|--------------|
| osint-api | FastAPI backend | 8000 | `/health` |
| osint-ui | Streamlit frontend | 8501 | HTTP 200 |
| postgres | PostgreSQL database | 5432 | Connection test |
| redis | Redis cache | 6379 | PING |
| nginx | Reverse proxy | 80/443 | HTTP 200 |

## 🛠️ Deployment Commands

### Using Deploy Script

```bash
# Build images
./docker/deploy.sh build

# Deploy production
./docker/deploy.sh deploy

# Deploy development
./docker/deploy.sh dev

# Show status
./docker/deploy.sh status

# View logs
./docker/deploy.sh logs

# Stop services
./docker/deploy.sh stop

# Clean up
./docker/deploy.sh cleanup
```

### Manual Docker Compose

```bash
# Production
docker-compose up -d

# Development
docker-compose -f docker-compose.dev.yml up -d

# Stop
docker-compose down

# View logs
docker-compose logs -f
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `OPENAI_API_KEY` | OpenAI API key | Yes | - |
| `SERPER_API_KEY` | Serper.dev API key | Yes | - |
| `POSTGRES_PASSWORD` | Database password | No | `osint_secure_password` |
| `HEADLESS_BROWSER` | Browser mode | No | `true` |
| `LOG_LEVEL` | Logging level | No | `INFO` |
| `DEBUG_MODE` | Debug mode | No | `false` |

### Volume Mounts

| Host Path | Container Path | Description |
|-----------|----------------|-------------|
| `./data` | `/app/data` | Vector indexes and crawled data |
| `./output` | `/app/output` | Analysis reports and results |
| `./logs` | `/app/logs` | Application logs |

## 🔍 Development

### Development Environment

The development environment includes:

- Live code reloading
- Jupyter notebook server
- Debug mode enabled
- Development tools (vim, htop, etc.)

```bash
# Start development environment
./docker/deploy.sh dev

# Access development container
docker exec -it osint-dev /bin/bash

# Run tests
docker exec -it osint-dev python -m pytest

# Start Jupyter
docker exec -it osint-dev jupyter lab --ip=0.0.0.0 --port=8888 --allow-root
```

### Custom Configuration

#### Nginx Configuration

Edit `docker/nginx/nginx.conf` to customize:
- Rate limiting
- SSL certificates
- Custom domains
- Load balancing

#### Database Schema

Modify `docker/postgres/init.sql` to:
- Add custom tables
- Create indexes
- Set up permissions
- Insert seed data

## 🚀 Production Deployment

### Security Considerations

1. **Environment Variables**: Use Docker secrets or external secret management
2. **SSL/TLS**: Configure SSL certificates in Nginx
3. **Database**: Use external managed database for production
4. **Monitoring**: Add monitoring and alerting
5. **Backups**: Implement database backup strategy

### Scaling

#### Horizontal Scaling

```yaml
# docker-compose.yml
services:
  osint-api:
    deploy:
      replicas: 3
    # ... other config
```

#### Resource Limits

```yaml
services:
  osint-api:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

### Health Monitoring

```bash
# Check service health
curl http://localhost:8000/health

# Monitor logs
docker-compose logs -f osint-api

# Resource usage
docker stats
```

## 🐛 Troubleshooting

### Common Issues

#### Port Conflicts

```bash
# Check port usage
netstat -tulpn | grep :8000

# Use different ports
export API_PORT=8001
export UI_PORT=8502
```

#### Memory Issues

```bash
# Check memory usage
docker stats

# Increase memory limits
# Edit docker-compose.yml memory settings
```

#### Database Connection

```bash
# Check database logs
docker-compose logs postgres

# Test connection
docker exec -it osint-postgres psql -U osint -d osint_framework
```

#### API Key Issues

```bash
# Verify environment variables
docker exec -it osint-api env | grep API_KEY

# Update .env file and restart
docker-compose restart
```

### Logs and Debugging

```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f osint-api

# Debug container
docker exec -it osint-api /bin/bash

# Check container health
docker inspect osint-api | grep Health
```

## 📊 Monitoring

### Built-in Health Checks

- API health endpoint: `/health`
- Database connection test
- Redis connectivity check
- Service dependency validation

### External Monitoring

Consider integrating:
- Prometheus + Grafana
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Datadog or New Relic
- Custom monitoring scripts

## 🔄 Updates and Maintenance

### Updating Images

```bash
# Pull latest base images
docker-compose pull

# Rebuild with latest code
./docker/deploy.sh build

# Rolling update
docker-compose up -d --force-recreate
```

### Database Migrations

```bash
# Backup database
docker exec osint-postgres pg_dump -U osint osint_framework > backup.sql

# Run migrations
docker exec -it osint-api python manage.py migrate

# Restore if needed
docker exec -i osint-postgres psql -U osint osint_framework < backup.sql
```

### Cleanup

```bash
# Remove unused images
docker image prune -f

# Remove unused volumes
docker volume prune -f

# Complete cleanup
./docker/deploy.sh cleanup
```

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [Production Deployment Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [Security Best Practices](https://docs.docker.com/engine/security/)

---

For more information, see the main [README.md](../README.md) file.
