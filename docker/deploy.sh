#!/bin/bash

# CrewAI OSINT Framework Docker Deployment Script
# This script helps deploy the OSINT framework using Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_DIR/.env"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}🧠 CrewAI OSINT Framework Docker${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_requirements() {
    print_info "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "All requirements met"
}

check_env_file() {
    print_info "Checking environment configuration..."
    
    if [ ! -f "$ENV_FILE" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        if [ -f "$PROJECT_DIR/.env.example" ]; then
            cp "$PROJECT_DIR/.env.example" "$ENV_FILE"
            print_warning "Please edit .env file and add your API keys before continuing."
            print_info "Required variables: OPENAI_API_KEY, SERPER_API_KEY"
            read -p "Press Enter after updating .env file..."
        else
            print_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi
    
    # Check for required environment variables
    source "$ENV_FILE"
    
    if [ -z "$OPENAI_API_KEY" ] || [ "$OPENAI_API_KEY" = "your_openai_api_key_here" ]; then
        print_warning "OPENAI_API_KEY not set in .env file"
    fi
    
    if [ -z "$SERPER_API_KEY" ] || [ "$SERPER_API_KEY" = "your_serper_api_key_here" ]; then
        print_warning "SERPER_API_KEY not set in .env file"
    fi
    
    print_success "Environment configuration checked"
}

build_images() {
    print_info "Building Docker images..."
    
    cd "$PROJECT_DIR"
    
    # Build production image
    docker build -t osint-framework:latest -f Dockerfile .
    
    # Build development image
    docker build -t osint-framework:dev -f Dockerfile.dev .
    
    print_success "Docker images built successfully"
}

deploy_production() {
    print_info "Deploying production environment..."
    
    cd "$PROJECT_DIR"
    
    # Stop existing containers
    docker-compose down 2>/dev/null || true
    
    # Start production environment
    docker-compose up -d
    
    # Wait for services to be ready
    print_info "Waiting for services to start..."
    sleep 30
    
    # Check service health
    if curl -f http://localhost:8000/health &> /dev/null; then
        print_success "API service is healthy"
    else
        print_warning "API service health check failed"
    fi
    
    print_success "Production environment deployed"
    print_info "API available at: http://localhost:8000"
    print_info "UI available at: http://localhost:8501"
}

deploy_development() {
    print_info "Deploying development environment..."
    
    cd "$PROJECT_DIR"
    
    # Stop existing containers
    docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    
    # Start development environment
    docker-compose -f docker-compose.dev.yml up -d
    
    print_success "Development environment deployed"
    print_info "Development container: osint-dev"
    print_info "Access with: docker exec -it osint-dev /bin/bash"
    print_info "Jupyter available at: http://localhost:8888"
}

show_status() {
    print_info "Service status:"
    docker-compose ps
    
    echo
    print_info "Container logs (last 10 lines):"
    docker-compose logs --tail=10
}

cleanup() {
    print_info "Cleaning up Docker resources..."
    
    cd "$PROJECT_DIR"
    
    # Stop and remove containers
    docker-compose down -v 2>/dev/null || true
    docker-compose -f docker-compose.dev.yml down -v 2>/dev/null || true
    
    # Remove images
    docker rmi osint-framework:latest osint-framework:dev 2>/dev/null || true
    
    # Clean up unused resources
    docker system prune -f
    
    print_success "Cleanup completed"
}

show_help() {
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  build       Build Docker images"
    echo "  deploy      Deploy production environment"
    echo "  dev         Deploy development environment"
    echo "  status      Show service status"
    echo "  logs        Show service logs"
    echo "  stop        Stop all services"
    echo "  cleanup     Clean up all Docker resources"
    echo "  help        Show this help message"
    echo
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 deploy"
    echo "  $0 dev"
    echo "  $0 status"
}

# Main script
main() {
    print_header
    
    case "${1:-help}" in
        "build")
            check_requirements
            check_env_file
            build_images
            ;;
        "deploy")
            check_requirements
            check_env_file
            build_images
            deploy_production
            ;;
        "dev")
            check_requirements
            check_env_file
            build_images
            deploy_development
            ;;
        "status")
            show_status
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "stop")
            print_info "Stopping all services..."
            docker-compose down
            docker-compose -f docker-compose.dev.yml down
            print_success "All services stopped"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function
main "$@"
