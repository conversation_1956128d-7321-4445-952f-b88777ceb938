events {
    worker_connections 1024;
}

http {
    upstream api {
        server osint-api:8000;
    }
    
    upstream ui {
        server osint-ui:8501;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=ui:10m rate=30r/s;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # API Server
    server {
        listen 80;
        server_name api.osint.local localhost;

        location / {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Health check endpoint
        location /health {
            access_log off;
            proxy_pass http://api/health;
        }
    }

    # UI Server
    server {
        listen 80;
        server_name ui.osint.local;

        location / {
            limit_req zone=ui burst=50 nodelay;
            
            proxy_pass http://ui;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Streamlit specific settings
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Accept-Encoding gzip;
        }

        # Static files
        location /_stcore/static {
            proxy_pass http://ui/_stcore/static;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Default server (redirect to UI)
    server {
        listen 80 default_server;
        server_name _;
        return 301 http://ui.osint.local$request_uri;
    }
}
