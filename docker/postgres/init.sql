-- CrewAI OSINT Framework Database Initialization
-- This script sets up the database schema for the OSINT framework

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS osint_framework;

-- Use the database
\c osint_framework;

-- Create tables for OSINT data storage

-- Analysis sessions table
CREATE TABLE IF NOT EXISTS analysis_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    agent_type VARCHAR(50) NOT NULL,
    query TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Analysis results table
CREATE TABLE IF NOT EXISTS analysis_results (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) REFERENCES analysis_sessions(session_id),
    result_type VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    confidence_score FLOAT,
    sources JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- I<PERSON> (Indicators of Compromise) table
CREATE TABLE IF NOT EXISTS iocs (
    id SERIAL PRIMARY KEY,
    ioc_type VARCHAR(50) NOT NULL,
    value VARCHAR(500) NOT NULL,
    confidence_score FLOAT,
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    threat_actor VARCHAR(255),
    campaign VARCHAR(255),
    tags TEXT[],
    metadata JSONB,
    UNIQUE(ioc_type, value)
);

-- Threat actors table
CREATE TABLE IF NOT EXISTS threat_actors (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    aliases TEXT[],
    description TEXT,
    targets TEXT[],
    ttps TEXT[],
    first_observed TIMESTAMP,
    last_activity TIMESTAMP,
    confidence_level VARCHAR(20),
    metadata JSONB
);

-- Geopolitical events table
CREATE TABLE IF NOT EXISTS geopolitical_events (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    region VARCHAR(100),
    country VARCHAR(100),
    impact_level VARCHAR(20),
    event_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sources JSONB,
    metadata JSONB
);

-- Search queries log
CREATE TABLE IF NOT EXISTS search_queries (
    id SERIAL PRIMARY KEY,
    query TEXT NOT NULL,
    query_type VARCHAR(50),
    agent_type VARCHAR(50),
    results_count INTEGER,
    execution_time FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Vector embeddings metadata
CREATE TABLE IF NOT EXISTS vector_embeddings (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255) UNIQUE NOT NULL,
    document_type VARCHAR(50),
    content_hash VARCHAR(64),
    embedding_model VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_analysis_sessions_agent_type ON analysis_sessions(agent_type);
CREATE INDEX IF NOT EXISTS idx_analysis_sessions_status ON analysis_sessions(status);
CREATE INDEX IF NOT EXISTS idx_analysis_sessions_created_at ON analysis_sessions(created_at);

CREATE INDEX IF NOT EXISTS idx_analysis_results_session_id ON analysis_results(session_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_result_type ON analysis_results(result_type);
CREATE INDEX IF NOT EXISTS idx_analysis_results_created_at ON analysis_results(created_at);

CREATE INDEX IF NOT EXISTS idx_iocs_type ON iocs(ioc_type);
CREATE INDEX IF NOT EXISTS idx_iocs_value ON iocs(value);
CREATE INDEX IF NOT EXISTS idx_iocs_threat_actor ON iocs(threat_actor);
CREATE INDEX IF NOT EXISTS idx_iocs_campaign ON iocs(campaign);

CREATE INDEX IF NOT EXISTS idx_threat_actors_name ON threat_actors(name);
CREATE INDEX IF NOT EXISTS idx_threat_actors_aliases ON threat_actors USING GIN(aliases);

CREATE INDEX IF NOT EXISTS idx_geopolitical_events_type ON geopolitical_events(event_type);
CREATE INDEX IF NOT EXISTS idx_geopolitical_events_region ON geopolitical_events(region);
CREATE INDEX IF NOT EXISTS idx_geopolitical_events_country ON geopolitical_events(country);
CREATE INDEX IF NOT EXISTS idx_geopolitical_events_date ON geopolitical_events(event_date);

CREATE INDEX IF NOT EXISTS idx_search_queries_type ON search_queries(query_type);
CREATE INDEX IF NOT EXISTS idx_search_queries_agent ON search_queries(agent_type);
CREATE INDEX IF NOT EXISTS idx_search_queries_created_at ON search_queries(created_at);

CREATE INDEX IF NOT EXISTS idx_vector_embeddings_document_id ON vector_embeddings(document_id);
CREATE INDEX IF NOT EXISTS idx_vector_embeddings_type ON vector_embeddings(document_type);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for analysis_sessions
CREATE TRIGGER update_analysis_sessions_updated_at 
    BEFORE UPDATE ON analysis_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert some initial data
INSERT INTO threat_actors (name, aliases, description, targets, ttps, confidence_level) VALUES
('APT1', ARRAY['Comment Crew', 'PLA Unit 61398'], 'Chinese military unit engaged in cyber espionage', ARRAY['Government', 'Defense', 'Technology'], ARRAY['Spear phishing', 'RATs', 'Data exfiltration'], 'high'),
('Lazarus Group', ARRAY['Hidden Cobra', 'Guardians of Peace'], 'North Korean state-sponsored group', ARRAY['Financial', 'Cryptocurrency', 'Government'], ARRAY['Destructive malware', 'Financial theft', 'Supply chain attacks'], 'high'),
('Fancy Bear', ARRAY['APT28', 'Sofacy', 'Sednit'], 'Russian military intelligence group', ARRAY['Government', 'Military', 'Political'], ARRAY['Spear phishing', 'Zero-day exploits', 'Credential harvesting'], 'high')
ON CONFLICT (name) DO NOTHING;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO osint;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO osint;
