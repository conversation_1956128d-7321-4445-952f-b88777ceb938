# 🧠 OSINT Agent Framework - API Documentation

## Overview

The OSINT Agent Framework provides a comprehensive REST API for conducting advanced Open Source Intelligence analysis. The API supports geopolitical analysis, cyber threat intelligence, workflow management, and security operations.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

The API uses JWT (JSON Web Token) based authentication. All protected endpoints require a valid bearer token.

### Login

**POST** `/auth/login`

Authenticate and receive an access token.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "token_type": "bearer",
  "expires_in": 1800
}
```

**Example:**
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### Using Authentication

Include the token in the Authorization header:

```bash
curl -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  "http://localhost:8000/api/v1/analysis/tasks"
```

## Geopolitical Analysis Endpoints

### Start Geopolitical Analysis

**POST** `/geopolitical/analyze`

Start a comprehensive geopolitical analysis.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Request Body:**
```json
{
  "topic": "string",
  "regions": ["string"],
  "time_range": "string",
  "analysis_type": "string",
  "include_economic": boolean,
  "include_security": boolean,
  "specific_topics": ["string"]
}
```

**Parameters:**
- `topic` (string): Analysis topic or query
- `regions` (array): List of regions to analyze (max 10)
- `time_range` (string): Time frame - one of: `1h`, `6h`, `24h`, `7d`, `30d`, `90d`
- `analysis_type` (string): Type of analysis - one of: `intelligence_brief`, `regional_monitoring`, `comprehensive`
- `include_economic` (boolean): Include economic analysis
- `include_security` (boolean): Include security assessment
- `specific_topics` (array): Specific topics to focus on

**Response:**
```json
{
  "task_id": "string",
  "status": "pending",
  "timestamp": "string",
  "message": "string"
}
```

**Example:**
```bash
curl -X POST "http://localhost:8000/api/v1/geopolitical/analyze" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "Middle East tensions",
    "regions": ["Middle East", "Europe"],
    "time_range": "7d",
    "analysis_type": "intelligence_brief",
    "include_economic": true,
    "include_security": true
  }'
```

### Generate Situation Report

**POST** `/geopolitical/situation-report`

Generate a multi-region situation report.

**Request Body:**
```json
{
  "regions": ["string"],
  "time_range": "string"
}
```

**Response:**
```json
{
  "task_id": "string",
  "status": "pending",
  "timestamp": "string"
}
```

## Cyber Threat Intelligence Endpoints

### Extract IOCs

**POST** `/cti/extract-iocs`

Extract Indicators of Compromise from threat data.

**Request Body:**
```json
{
  "threat_data": "string",
  "ioc_types": ["string"],
  "confidence_threshold": number
}
```

**Parameters:**
- `threat_data` (string): Raw threat report or security data
- `ioc_types` (array, optional): Specific IOC types to extract
- `confidence_threshold` (number, optional): Minimum confidence score (0.0-1.0)

**Response:**
```json
{
  "task_id": "string",
  "status": "pending",
  "timestamp": "string"
}
```

### Track Threat Actor

**POST** `/cti/track-actor`

Track and analyze threat actor activities.

**Request Body:**
```json
{
  "actor_name": "string",
  "search_type": "string",
  "time_range": "string"
}
```

**Parameters:**
- `actor_name` (string): Name or identifier of threat actor
- `search_type` (string): Type of search - `recent_activity`, `profile`, `ttps`, `attribution`
- `time_range` (string): Time frame for analysis

## Task Management Endpoints

### List Tasks

**GET** `/analysis/tasks`

Retrieve all analysis tasks.

**Query Parameters:**
- `status` (string, optional): Filter by status - `pending`, `running`, `completed`, `failed`
- `type` (string, optional): Filter by task type
- `limit` (integer, optional): Maximum number of results (default: 50)
- `offset` (integer, optional): Pagination offset (default: 0)

**Response:**
```json
{
  "tasks": [
    {
      "id": "string",
      "type": "string",
      "status": "string",
      "created_at": "string",
      "updated_at": "string",
      "result": "object"
    }
  ],
  "total": number,
  "limit": number,
  "offset": number
}
```

### Get Task Status

**GET** `/analysis/tasks/{task_id}`

Get detailed information about a specific task.

**Response:**
```json
{
  "id": "string",
  "type": "string",
  "status": "string",
  "request_data": "object",
  "result": "object",
  "error": "string",
  "created_at": "string",
  "updated_at": "string",
  "progress": number
}
```

### Cancel Task

**DELETE** `/analysis/tasks/{task_id}`

Cancel a running task.

**Response:**
```json
{
  "status": "cancelled",
  "task_id": "string",
  "timestamp": "string"
}
```

## Security Endpoints

### Encrypt Data

**POST** `/security/encrypt`

Encrypt sensitive data.

**Request Body:**
```json
{
  "data": "string"
}
```

**Response:**
```json
{
  "status": "success",
  "encrypted_data": "string",
  "timestamp": "string"
}
```

### Decrypt Data

**POST** `/security/decrypt`

Decrypt previously encrypted data.

**Request Body:**
```json
{
  "encrypted_data": "string"
}
```

**Response:**
```json
{
  "status": "success",
  "decrypted_data": "string",
  "timestamp": "string"
}
```

### Scan for Sensitive Data

**POST** `/security/scan-sensitive`

Scan text for sensitive information.

**Request Body:**
```json
{
  "text": "string"
}
```

**Response:**
```json
{
  "status": "success",
  "sensitive_data_found": [
    {
      "type": "string",
      "value": "string",
      "confidence": number,
      "position": {
        "start": number,
        "end": number
      }
    }
  ],
  "scan_timestamp": "string"
}
```

## Workflow Management Endpoints

### Execute Workflow

**POST** `/workflows/execute`

Execute a predefined workflow.

**Request Body:**
```json
{
  "workflow_id": "string",
  "initial_context": "object"
}
```

### List Workflows

**GET** `/workflows`

List all available workflows.

**Response:**
```json
{
  "workflows": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "version": "string",
      "task_count": number
    }
  ],
  "total_count": number
}
```

## Error Handling

The API uses standard HTTP status codes and returns detailed error information.

### Error Response Format

```json
{
  "detail": "string",
  "error_code": "string",
  "timestamp": "string",
  "request_id": "string"
}
```

### Common Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `413` - Request Too Large
- `422` - Unprocessable Entity (validation error)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error
- `503` - Service Unavailable

### Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Authentication endpoints**: 5 requests per minute
- **Analysis endpoints**: 10 requests per minute
- **General endpoints**: 100 requests per minute

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## SDK and Client Libraries

### Python SDK

```python
from osint_framework import OSINTClient

client = OSINTClient(
    base_url="http://localhost:8000",
    username="admin",
    password="admin123"
)

# Start geopolitical analysis
task = client.geopolitical.analyze(
    topic="Regional tensions",
    regions=["Middle East"],
    time_range="7d"
)

# Get results
result = client.tasks.get_result(task.task_id)
```

### JavaScript SDK

```javascript
import { OSINTClient } from '@osint-framework/client';

const client = new OSINTClient({
  baseURL: 'http://localhost:8000',
  username: 'admin',
  password: 'admin123'
});

// Start analysis
const task = await client.geopolitical.analyze({
  topic: 'Regional tensions',
  regions: ['Middle East'],
  timeRange: '7d'
});

// Get results
const result = await client.tasks.getResult(task.taskId);
```
