# 🧠 OSINT Agent Framework - Deployment Guide

## Table of Contents

1. [Deployment Overview](#deployment-overview)
2. [Prerequisites](#prerequisites)
3. [Local Development Deployment](#local-development-deployment)
4. [Production Deployment](#production-deployment)
5. [Docker Deployment](#docker-deployment)
6. [Kubernetes Deployment](#kubernetes-deployment)
7. [Cloud Deployment](#cloud-deployment)
8. [Security Configuration](#security-configuration)
9. [Monitoring and Maintenance](#monitoring-and-maintenance)
10. [Troubleshooting](#troubleshooting)

## Deployment Overview

The OSINT Agent Framework can be deployed in various configurations depending on your requirements:

- **Development**: Single-machine setup for development and testing
- **Production**: Multi-service production deployment with load balancing
- **Docker**: Containerized deployment for consistency and portability
- **Kubernetes**: Orchestrated deployment for scalability and high availability
- **Cloud**: Managed cloud deployment on AWS, Azure, or GCP

### Architecture Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Gateway   │    │   API Gateway   │
│   (nginx/ALB)   │    │   (nginx)       │    │   (FastAPI)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Application Layer                  │
         │  ┌─────────┐  ┌─────────┐  ┌─────────┐         │
         │  │ Agents  │  │Workflows│  │  Tools  │         │
         │  └─────────┘  └─────────┘  └─────────┘         │
         └─────────────────────┬───────────────────────────┘
                               │
    ┌──────────────┬───────────┼───────────┬──────────────┐
    │              │           │           │              │
┌───▼───┐    ┌─────▼─────┐ ┌───▼───┐ ┌─────▼─────┐ ┌──────▼──────┐
│Database│    │   Cache   │ │ Queue │ │  Storage  │ │   Logging   │
│(SQLite/│    │  (Redis)  │ │(Celery│ │   (S3)    │ │(ELK Stack)  │
│Postgres│    │           │ │/RQ)   │ │           │ │             │
└───────┘    └───────────┘ └───────┘ └───────────┘ └─────────────┘
```

## Prerequisites

### System Requirements

#### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB free space
- **OS**: Linux (Ubuntu 18.04+), macOS (10.15+), Windows 10+

#### Recommended Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Network**: Stable internet connection

### Software Dependencies

- **Python**: 3.8 or higher
- **Node.js**: 14+ (for UI development)
- **Docker**: 20.10+ (for containerized deployment)
- **Git**: Latest version

### API Keys and Credentials

Required API keys:
- **OpenAI API Key**: For AI/ML functionality
- **Serper API Key**: For search capabilities

Optional services:
- **Redis**: For caching and session storage
- **PostgreSQL**: For production database
- **AWS/Azure/GCP**: For cloud deployment

## Local Development Deployment

### Quick Start

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-org/osint-framework.git
   cd osint-framework
   ```

2. **Setup Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

4. **Initialize Database**
   ```bash
   python -m osint_framework init-db
   ```

5. **Start Services**
   ```bash
   # Terminal 1: API Server
   python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
   
   # Terminal 2: Web Interface
   streamlit run ui/streamlit_app.py --server.port 8501
   ```

6. **Verify Installation**
   - API: http://localhost:8000/docs
   - Web UI: http://localhost:8501
   - Health Check: http://localhost:8000/api/v1/health

### Development Configuration

Create a development configuration file:

```python
# config/development.py
import os

class DevelopmentConfig:
    """Development environment configuration."""
    
    # Debug settings
    DEBUG = True
    TESTING = False
    
    # Database
    DATABASE_URL = "sqlite:///data/osint_dev.db"
    
    # Security (use simple keys for development)
    SECRET_KEY = "dev-secret-key-change-in-production"
    ENCRYPTION_KEY = "dev-encryption-key-change-in-production"
    
    # External APIs
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    SERPER_API_KEY = os.getenv("SERPER_API_KEY")
    
    # Performance
    MAX_WORKERS = 2
    CACHE_TTL = 300  # 5 minutes
    
    # Logging
    LOG_LEVEL = "DEBUG"
    LOG_FILE = "logs/osint_dev.log"
```

## Production Deployment

### Production Setup

1. **Server Preparation**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install dependencies
   sudo apt install -y python3.9 python3.9-venv python3-pip nginx postgresql redis-server
   
   # Create application user
   sudo useradd -m -s /bin/bash osint
   sudo usermod -aG sudo osint
   ```

2. **Application Deployment**
   ```bash
   # Switch to application user
   sudo su - osint
   
   # Clone and setup
   git clone https://github.com/your-org/osint-framework.git
   cd osint-framework
   python3.9 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. **Database Setup**
   ```bash
   # PostgreSQL setup
   sudo -u postgres createuser osint
   sudo -u postgres createdb osint_production -O osint
   sudo -u postgres psql -c "ALTER USER osint PASSWORD 'secure_password';"
   ```

4. **Environment Configuration**
   ```bash
   # Production environment file
   cat > .env << EOF
   # Production Configuration
   ENVIRONMENT=production
   DEBUG=false
   
   # Database
   DATABASE_URL=postgresql://osint:secure_password@localhost/osint_production
   
   # Security
   SECRET_KEY=$(openssl rand -hex 32)
   ENCRYPTION_KEY=$(python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
   
   # API Keys
   OPENAI_API_KEY=your_openai_api_key
   SERPER_API_KEY=your_serper_api_key
   
   # Performance
   MAX_WORKERS=8
   CACHE_TTL=3600
   
   # Security
   ALLOWED_HOSTS=your-domain.com,www.your-domain.com
   CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
   EOF
   ```

5. **Service Configuration**
   
   Create systemd service files:
   
   ```ini
   # /etc/systemd/system/osint-api.service
   [Unit]
   Description=OSINT Framework API
   After=network.target postgresql.service redis.service
   
   [Service]
   Type=exec
   User=osint
   Group=osint
   WorkingDirectory=/home/<USER>/osint-framework
   Environment=PATH=/home/<USER>/osint-framework/venv/bin
   ExecStart=/home/<USER>/osint-framework/venv/bin/uvicorn api.main:app --host 127.0.0.1 --port 8000 --workers 4
   Restart=always
   RestartSec=3
   
   [Install]
   WantedBy=multi-user.target
   ```
   
   ```ini
   # /etc/systemd/system/osint-ui.service
   [Unit]
   Description=OSINT Framework UI
   After=network.target osint-api.service
   
   [Service]
   Type=exec
   User=osint
   Group=osint
   WorkingDirectory=/home/<USER>/osint-framework
   Environment=PATH=/home/<USER>/osint-framework/venv/bin
   ExecStart=/home/<USER>/osint-framework/venv/bin/streamlit run ui/streamlit_app.py --server.port 8501 --server.address 127.0.0.1
   Restart=always
   RestartSec=3
   
   [Install]
   WantedBy=multi-user.target
   ```

6. **Nginx Configuration**
   ```nginx
   # /etc/nginx/sites-available/osint-framework
   server {
       listen 80;
       server_name your-domain.com www.your-domain.com;
       
       # Redirect HTTP to HTTPS
       return 301 https://$server_name$request_uri;
   }
   
   server {
       listen 443 ssl http2;
       server_name your-domain.com www.your-domain.com;
       
       # SSL Configuration
       ssl_certificate /path/to/ssl/certificate.crt;
       ssl_certificate_key /path/to/ssl/private.key;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
       
       # Security headers
       add_header X-Frame-Options DENY;
       add_header X-Content-Type-Options nosniff;
       add_header X-XSS-Protection "1; mode=block";
       add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
       
       # API proxy
       location /api/ {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_read_timeout 300s;
           proxy_connect_timeout 75s;
       }
       
       # UI proxy
       location / {
           proxy_pass http://127.0.0.1:8501;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           
           # WebSocket support for Streamlit
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_read_timeout 86400;
       }
       
       # Static files
       location /static/ {
           alias /home/<USER>/osint-framework/static/;
           expires 1y;
           add_header Cache-Control "public, immutable";
       }
   }
   ```

7. **Start Services**
   ```bash
   # Enable and start services
   sudo systemctl enable osint-api osint-ui nginx postgresql redis
   sudo systemctl start osint-api osint-ui nginx postgresql redis
   
   # Check status
   sudo systemctl status osint-api osint-ui
   ```

## Docker Deployment

### Docker Configuration

1. **Dockerfile**
   ```dockerfile
   FROM python:3.9-slim
   
   # Set environment variables
   ENV PYTHONDONTWRITEBYTECODE=1
   ENV PYTHONUNBUFFERED=1
   ENV DEBIAN_FRONTEND=noninteractive
   
   # Install system dependencies
   RUN apt-get update && apt-get install -y \
       build-essential \
       curl \
       && rm -rf /var/lib/apt/lists/*
   
   # Create application directory
   WORKDIR /app
   
   # Install Python dependencies
   COPY requirements.txt .
   RUN pip install --no-cache-dir -r requirements.txt
   
   # Copy application code
   COPY . .
   
   # Create non-root user
   RUN useradd -m -u 1000 osint && chown -R osint:osint /app
   USER osint
   
   # Expose ports
   EXPOSE 8000 8501
   
   # Health check
   HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
       CMD curl -f http://localhost:8000/api/v1/health || exit 1
   
   # Default command
   CMD ["python", "deploy.py"]
   ```

2. **Docker Compose**
   ```yaml
   version: '3.8'
   
   services:
     db:
       image: postgres:13
       environment:
         POSTGRES_DB: osint_production
         POSTGRES_USER: osint
         POSTGRES_PASSWORD: ${DB_PASSWORD}
       volumes:
         - postgres_data:/var/lib/postgresql/data
       networks:
         - osint-network
   
     redis:
       image: redis:6-alpine
       networks:
         - osint-network
   
     api:
       build: .
       environment:
         - DATABASE_URL=postgresql://osint:${DB_PASSWORD}@db:5432/osint_production
         - REDIS_URL=redis://redis:6379
         - OPENAI_API_KEY=${OPENAI_API_KEY}
         - SERPER_API_KEY=${SERPER_API_KEY}
       depends_on:
         - db
         - redis
       networks:
         - osint-network
       command: ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]
   
     ui:
       build: .
       environment:
         - API_URL=http://api:8000
       depends_on:
         - api
       networks:
         - osint-network
       command: ["streamlit", "run", "ui/streamlit_app.py", "--server.port", "8501", "--server.address", "0.0.0.0"]
   
     nginx:
       image: nginx:alpine
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./nginx.conf:/etc/nginx/nginx.conf
         - ./ssl:/etc/nginx/ssl
       depends_on:
         - api
         - ui
       networks:
         - osint-network
   
   volumes:
     postgres_data:
   
   networks:
     osint-network:
       driver: bridge
   ```

3. **Deployment Script**
   ```bash
   #!/bin/bash
   # deploy.sh
   
   set -e
   
   echo "🚀 Starting OSINT Framework deployment..."
   
   # Build and start services
   docker-compose build
   docker-compose up -d
   
   # Wait for services to be ready
   echo "⏳ Waiting for services to start..."
   sleep 30
   
   # Run database migrations
   docker-compose exec api python -m osint_framework migrate
   
   # Create admin user
   docker-compose exec api python -m osint_framework create-admin
   
   # Health check
   if curl -f http://localhost/api/v1/health; then
       echo "✅ Deployment successful!"
       echo "🌐 Web UI: http://localhost"
       echo "📚 API Docs: http://localhost/api/docs"
   else
       echo "❌ Deployment failed!"
       exit 1
   fi
   ```

## Kubernetes Deployment

### Kubernetes Manifests

1. **Namespace**
   ```yaml
   # k8s/namespace.yaml
   apiVersion: v1
   kind: Namespace
   metadata:
     name: osint-framework
   ```

2. **ConfigMap**
   ```yaml
   # k8s/configmap.yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: osint-config
     namespace: osint-framework
   data:
     DATABASE_URL: "*****************************************/osint_production"
     REDIS_URL: "redis://redis:6379"
     LOG_LEVEL: "INFO"
     MAX_WORKERS: "8"
   ```

3. **Secrets**
   ```yaml
   # k8s/secrets.yaml
   apiVersion: v1
   kind: Secret
   metadata:
     name: osint-secrets
     namespace: osint-framework
   type: Opaque
   data:
     openai-api-key: <base64-encoded-key>
     serper-api-key: <base64-encoded-key>
     secret-key: <base64-encoded-key>
     encryption-key: <base64-encoded-key>
   ```

4. **Deployment**
   ```yaml
   # k8s/deployment.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: osint-api
     namespace: osint-framework
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: osint-api
     template:
       metadata:
         labels:
           app: osint-api
       spec:
         containers:
         - name: api
           image: osint-framework:latest
           ports:
           - containerPort: 8000
           env:
           - name: OPENAI_API_KEY
             valueFrom:
               secretKeyRef:
                 name: osint-secrets
                 key: openai-api-key
           envFrom:
           - configMapRef:
               name: osint-config
           resources:
             requests:
               memory: "512Mi"
               cpu: "250m"
             limits:
               memory: "1Gi"
               cpu: "500m"
           livenessProbe:
             httpGet:
               path: /api/v1/health
               port: 8000
             initialDelaySeconds: 30
             periodSeconds: 10
           readinessProbe:
             httpGet:
               path: /api/v1/health
               port: 8000
             initialDelaySeconds: 5
             periodSeconds: 5
   ```

5. **Service**
   ```yaml
   # k8s/service.yaml
   apiVersion: v1
   kind: Service
   metadata:
     name: osint-api-service
     namespace: osint-framework
   spec:
     selector:
       app: osint-api
     ports:
     - protocol: TCP
       port: 80
       targetPort: 8000
     type: ClusterIP
   ```

6. **Ingress**
   ```yaml
   # k8s/ingress.yaml
   apiVersion: networking.k8s.io/v1
   kind: Ingress
   metadata:
     name: osint-ingress
     namespace: osint-framework
     annotations:
       kubernetes.io/ingress.class: nginx
       cert-manager.io/cluster-issuer: letsencrypt-prod
       nginx.ingress.kubernetes.io/ssl-redirect: "true"
   spec:
     tls:
     - hosts:
       - osint.your-domain.com
       secretName: osint-tls
     rules:
     - host: osint.your-domain.com
       http:
         paths:
         - path: /api
           pathType: Prefix
           backend:
             service:
               name: osint-api-service
               port:
                 number: 80
         - path: /
           pathType: Prefix
           backend:
             service:
               name: osint-ui-service
               port:
                 number: 80
   ```

### Deployment Commands

```bash
# Apply all manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n osint-framework

# View logs
kubectl logs -f deployment/osint-api -n osint-framework

# Scale deployment
kubectl scale deployment osint-api --replicas=5 -n osint-framework
```
