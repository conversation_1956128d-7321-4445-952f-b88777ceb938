# 🧠 OSINT Agent Framework - Developer Guide

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Development Setup](#development-setup)
3. [Code Structure](#code-structure)
4. [Creating Custom Agents](#creating-custom-agents)
5. [Workflow Development](#workflow-development)
6. [API Development](#api-development)
7. [Testing Guidelines](#testing-guidelines)
8. [Deployment](#deployment)
9. [Contributing](#contributing)

## Architecture Overview

The OSINT Agent Framework follows a modular, microservices-inspired architecture designed for scalability, maintainability, and extensibility.

### Core Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI        │    │   API Gateway   │    │   CLI Tools     │
│  (Streamlit)    │    │   (FastAPI)     │    │   (Click)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Core Framework                     │
         └─────────────────────┬───────────────────────────┘
                               │
    ┌──────────────┬───────────┼───────────┬──────────────┐
    │              │           │           │              │
┌───▼───┐    ┌─────▼─────┐ ┌───▼───┐ ┌─────▼─────┐ ┌──────▼──────┐
│Agents │    │Workflows  │ │ Tools │ │    RAG    │ │ Utilities   │
│       │    │           │ │       │ │  System   │ │             │
└───────┘    └───────────┘ └───────┘ └───────────┘ └─────────────┘
```

### Technology Stack

- **Backend**: Python 3.8+, FastAPI, Pydantic
- **Frontend**: <PERSON>lit, <PERSON><PERSON>ly, HTML/CSS/JavaScript
- **AI/ML**: OpenAI GPT, LangChain, LlamaIndex, DSPy
- **Data**: SQLite/PostgreSQL, Redis (optional)
- **Security**: Cryptography, JWT, bcrypt
- **Testing**: pytest, pytest-asyncio, pytest-cov
- **Documentation**: Markdown, Sphinx (optional)

### Design Principles

1. **Modularity**: Each component is self-contained and loosely coupled
2. **Extensibility**: Easy to add new agents, tools, and workflows
3. **Security**: Security-first design with encryption and access controls
4. **Performance**: Optimized for concurrent processing and caching
5. **Maintainability**: Clean code, comprehensive testing, and documentation

## Development Setup

### Prerequisites

- Python 3.8 or higher
- Git
- Virtual environment tool (venv, conda, or virtualenv)
- Code editor (VS Code, PyCharm, or similar)

### Environment Setup

1. **Clone and Setup**
   ```bash
   git clone https://github.com/your-org/osint-framework.git
   cd osint-framework
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   ```

2. **Install Development Dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

3. **Pre-commit Hooks**
   ```bash
   pre-commit install
   ```

4. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

### Development Tools

#### Code Quality Tools

```bash
# Code formatting
black agents/ tools/ workflows/ api/ utils/ rag/

# Import sorting
isort agents/ tools/ workflows/ api/ utils/ rag/

# Linting
flake8 agents/ tools/ workflows/ api/ utils/ rag/

# Type checking
mypy agents/ tools/ workflows/ api/ utils/ rag/
```

#### Testing

```bash
# Run all tests
python run_tests.py --all

# Run specific test types
python run_tests.py --unit
python run_tests.py --integration
python run_tests.py --security

# Run with coverage
python run_tests.py --all --coverage
```

## Code Structure

### Directory Layout

```
osint-framework/
├── agents/                 # AI agents for different domains
│   ├── __init__.py
│   ├── base_agent.py      # Base agent class
│   ├── geo_agent.py       # Geopolitical analysis agent
│   └── cti_agent.py       # Cyber threat intelligence agent
├── api/                   # FastAPI application
│   ├── __init__.py
│   ├── main.py           # Main API application
│   ├── models.py         # Pydantic models
│   └── dependencies.py   # Dependency injection
├── tools/                 # Individual analysis tools
│   ├── __init__.py
│   ├── base_tool.py      # Base tool class
│   ├── search_tools.py   # Search and OSINT tools
│   └── crawl4ai_wrapper.py # Web crawling tools
├── workflows/             # Multi-step analysis workflows
│   ├── __init__.py
│   ├── workflow_engine.py # Workflow orchestration
│   ├── langchain_geo_workflow.py
│   └── llamaindex_cti_workflow.py
├── rag/                   # Retrieval-Augmented Generation
│   ├── __init__.py
│   ├── index_builder.py  # Document indexing
│   └── retriever.py      # Information retrieval
├── utils/                 # Utility modules
│   ├── __init__.py
│   ├── logging_config.py # Logging configuration
│   ├── security.py       # Security utilities
│   └── monitoring.py     # System monitoring
├── ui/                    # User interface
│   ├── streamlit_app.py  # Main Streamlit application
│   └── README.md
├── tests/                 # Test suite
│   ├── unit/             # Unit tests
│   ├── integration/      # Integration tests
│   └── e2e/              # End-to-end tests
├── docs/                  # Documentation
├── data/                  # Data storage
├── logs/                  # Log files
├── requirements.txt       # Production dependencies
├── requirements-dev.txt   # Development dependencies
├── pytest.ini           # Pytest configuration
├── run_tests.py          # Test runner
└── README.md
```

### Coding Standards

#### Python Style Guide

Follow PEP 8 with these specific guidelines:

- **Line Length**: 100 characters maximum
- **Imports**: Use absolute imports, group by standard/third-party/local
- **Naming**: 
  - Classes: `PascalCase`
  - Functions/Variables: `snake_case`
  - Constants: `UPPER_SNAKE_CASE`
- **Docstrings**: Google-style docstrings for all public functions/classes

#### Example Code Structure

```python
"""
Module docstring describing the purpose and functionality.
"""

import os
import sys
from typing import Dict, List, Optional

from third_party_library import SomeClass

from utils.logging_config import osint_logger


class ExampleAgent:
    """
    Example agent demonstrating coding standards.
    
    This agent provides example functionality for the OSINT framework
    following established patterns and best practices.
    
    Attributes:
        name: Agent name identifier
        config: Configuration dictionary
        logger: Logger instance for this agent
    """
    
    def __init__(self, name: str, config: Optional[Dict] = None):
        """
        Initialize the example agent.
        
        Args:
            name: Unique name for this agent instance
            config: Optional configuration dictionary
            
        Raises:
            ValueError: If name is empty or invalid
        """
        if not name or not isinstance(name, str):
            raise ValueError("Agent name must be a non-empty string")
            
        self.name = name
        self.config = config or {}
        self.logger = osint_logger.logger
        
        self.logger.info(f"Initialized {self.__class__.__name__}", agent_name=name)
    
    async def execute_analysis(self, query: str) -> Dict[str, Any]:
        """
        Execute analysis based on the provided query.
        
        Args:
            query: Analysis query or request
            
        Returns:
            Dictionary containing analysis results
            
        Raises:
            ValueError: If query is invalid
            RuntimeError: If analysis execution fails
        """
        try:
            self.logger.info("Starting analysis", query=query)
            
            # Validation
            if not query or len(query.strip()) == 0:
                raise ValueError("Query cannot be empty")
            
            # Analysis logic here
            result = await self._perform_analysis(query)
            
            self.logger.info("Analysis completed successfully")
            return result
            
        except Exception as e:
            self.logger.error(f"Analysis failed: {e}")
            raise RuntimeError(f"Analysis execution failed: {e}")
    
    async def _perform_analysis(self, query: str) -> Dict[str, Any]:
        """Private method for actual analysis implementation."""
        # Implementation details
        return {"status": "success", "query": query, "results": []}
```

## Creating Custom Agents

### Agent Base Class

All agents should inherit from the `BaseAgent` class:

```python
from agents.base_agent import BaseAgent
from typing import Dict, Any

class CustomAgent(BaseAgent):
    """Custom agent for specialized analysis."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.agent_type = "custom"
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute custom analysis logic."""
        # Implement your analysis logic here
        return {"status": "success", "results": "Custom analysis results"}
```

### Agent Registration

Register your agent in the system:

```python
# In agents/__init__.py
from .custom_agent import CustomAgent

AVAILABLE_AGENTS = {
    "geopolitical": GeopoliticalAgent,
    "cti": CTIAgent,
    "custom": CustomAgent,  # Add your agent here
}
```

### Agent Configuration

Configure your agent using the configuration system:

```python
# In config/agents.yaml
custom_agent:
  enabled: true
  max_concurrent: 2
  timeout: 300
  parameters:
    custom_param1: "value1"
    custom_param2: "value2"
```

## Workflow Development

### Workflow Structure

Workflows are defined using a declarative structure:

```python
from workflows.workflow_engine import WorkflowDefinition, Task, TaskType

workflow = WorkflowDefinition(
    id="custom-analysis-workflow",
    name="Custom Analysis Workflow",
    description="Performs custom multi-step analysis",
    version="1.0.0",
    tasks=[
        Task(
            id="data-collection",
            name="Data Collection",
            task_type=TaskType.AGENT_TASK,
            agent_type="custom",
            parameters={"source": "web"},
            dependencies=[]
        ),
        Task(
            id="analysis",
            name="Analysis",
            task_type=TaskType.AGENT_TASK,
            agent_type="custom",
            parameters={"analysis_type": "deep"},
            dependencies=["data-collection"]
        )
    ]
)
```

### Custom Task Types

Create custom task types for specialized operations:

```python
from workflows.workflow_engine import TaskHandler

class CustomTaskHandler(TaskHandler):
    """Custom task handler for specialized operations."""
    
    async def execute(self, task: Task, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute custom task logic."""
        # Implement your task logic here
        return {"status": "completed", "result": "Custom task result"}

# Register the handler
workflow_engine.register_task_handler("custom_task", CustomTaskHandler())
```

## API Development

### Adding New Endpoints

1. **Define Pydantic Models**
   ```python
   # In api/models.py
   from pydantic import BaseModel
   
   class CustomAnalysisRequest(BaseModel):
       query: str
       parameters: Dict[str, Any] = {}
   
   class CustomAnalysisResponse(BaseModel):
       task_id: str
       status: str
       timestamp: str
   ```

2. **Implement Endpoint**
   ```python
   # In api/main.py
   @app.post("/api/v1/custom/analyze", response_model=CustomAnalysisResponse)
   async def custom_analysis(
       request: CustomAnalysisRequest,
       current_user: dict = Depends(require_permission("read"))
   ):
       """Execute custom analysis."""
       try:
           # Validation
           validate_api_keys()
           sanitized_query = sanitize_input(request.query)
           
           # Create task
           task_id = create_task("custom_analysis", request.dict())
           
           # Execute in background
           background_tasks.add_task(run_custom_analysis, task_id, request)
           
           return CustomAnalysisResponse(
               task_id=task_id,
               status="pending",
               timestamp=datetime.now().isoformat()
           )
           
       except Exception as e:
           osint_logger.log_error(e, "custom_analysis")
           raise HTTPException(status_code=500, detail=str(e))
   ```

### API Testing

Create comprehensive tests for your endpoints:

```python
# In tests/integration/api/test_custom_endpoints.py
def test_custom_analysis_endpoint(api_client, mock_env_vars):
    """Test custom analysis endpoint."""
    # Login first
    login_response = api_client.post(
        "/api/v1/auth/login",
        json={"username": "admin", "password": "admin123"}
    )
    token = login_response.json()["access_token"]
    
    # Test custom analysis
    response = api_client.post(
        "/api/v1/custom/analyze",
        json={"query": "test query", "parameters": {}},
        headers={"Authorization": f"Bearer {token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "task_id" in data
    assert data["status"] == "pending"
```

## Testing Guidelines

### Test Structure

Follow the testing pyramid:

- **Unit Tests (70%)**: Test individual functions and classes
- **Integration Tests (20%)**: Test component interactions
- **End-to-End Tests (10%)**: Test complete user workflows

### Writing Tests

#### Unit Test Example

```python
# tests/unit/agents/test_custom_agent.py
import pytest
from agents.custom_agent import CustomAgent

class TestCustomAgent:
    """Test cases for CustomAgent."""
    
    @pytest.fixture
    def agent(self):
        """Create agent instance for testing."""
        return CustomAgent(name="test-agent")
    
    def test_agent_initialization(self, agent):
        """Test agent initialization."""
        assert agent.name == "test-agent"
        assert agent.agent_type == "custom"
    
    @pytest.mark.asyncio
    async def test_execute_analysis(self, agent):
        """Test analysis execution."""
        result = await agent.execute({"query": "test query"})
        
        assert result["status"] == "success"
        assert "results" in result
```

#### Integration Test Example

```python
# tests/integration/workflows/test_custom_workflow.py
import pytest
from workflows.workflow_engine import WorkflowEngine

@pytest.mark.asyncio
async def test_custom_workflow_execution(workflow_engine, sample_workflow):
    """Test custom workflow execution."""
    # Register workflow
    workflow_engine.register_workflow(sample_workflow)
    
    # Execute workflow
    execution_id = await workflow_engine.execute_workflow(
        workflow_id=sample_workflow.id,
        initial_context={"test": "data"}
    )
    
    # Wait for completion
    execution = await workflow_engine.wait_for_completion(execution_id)
    
    assert execution.status == WorkflowStatus.COMPLETED
    assert execution.task_results is not None
```

### Test Data Management

Use fixtures for test data:

```python
# tests/conftest.py
@pytest.fixture
def sample_custom_data():
    """Sample data for custom agent testing."""
    return {
        "query": "test analysis query",
        "parameters": {
            "depth": "detailed",
            "sources": ["web", "social"]
        },
        "expected_iocs": ["***********", "malicious.com"]
    }
```

## Deployment

### Production Deployment

#### Docker Deployment

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000 8501

CMD ["python", "deploy.py"]
```

#### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SERPER_API_KEY=${SERPER_API_KEY}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
  
  ui:
    build: .
    ports:
      - "8501:8501"
    depends_on:
      - api
    command: ["streamlit", "run", "ui/streamlit_app.py"]
```

#### Kubernetes Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: osint-framework
spec:
  replicas: 3
  selector:
    matchLabels:
      app: osint-framework
  template:
    metadata:
      labels:
        app: osint-framework
    spec:
      containers:
      - name: api
        image: osint-framework:latest
        ports:
        - containerPort: 8000
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: openai-key
```

### Environment Configuration

#### Production Settings

```python
# config/production.py
import os

class ProductionConfig:
    """Production configuration settings."""
    
    # Security
    SECRET_KEY = os.getenv("SECRET_KEY")
    ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY")
    
    # Database
    DATABASE_URL = os.getenv("DATABASE_URL")
    
    # Performance
    MAX_WORKERS = int(os.getenv("MAX_WORKERS", "8"))
    CACHE_TTL = int(os.getenv("CACHE_TTL", "3600"))
    
    # Monitoring
    ENABLE_METRICS = True
    LOG_LEVEL = "INFO"
```

## Contributing

### Development Workflow

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/osint-framework.git
   cd osint-framework
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make Changes**
   - Follow coding standards
   - Add tests for new functionality
   - Update documentation

4. **Test Changes**
   ```bash
   python run_tests.py --all
   python run_tests.py --lint
   ```

5. **Submit Pull Request**
   - Clear description of changes
   - Reference related issues
   - Include test results

### Code Review Process

All contributions go through code review:

1. **Automated Checks**: CI/CD pipeline runs tests and quality checks
2. **Peer Review**: At least one maintainer reviews the code
3. **Documentation Review**: Ensure documentation is updated
4. **Security Review**: Security-sensitive changes get additional review

### Release Process

1. **Version Bump**: Update version numbers
2. **Changelog**: Update CHANGELOG.md
3. **Testing**: Comprehensive testing on staging environment
4. **Documentation**: Update documentation
5. **Release**: Create GitHub release and deploy
