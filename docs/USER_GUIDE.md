# 🧠 OSINT Agent Framework - User Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Web Interface Guide](#web-interface-guide)
4. [Geopolitical Analysis](#geopolitical-analysis)
5. [Cyber Threat Intelligence](#cyber-threat-intelligence)
6. [Workflow Management](#workflow-management)
7. [Security Features](#security-features)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Introduction

The OSINT Agent Framework is a comprehensive platform for conducting advanced Open Source Intelligence analysis. It combines the power of AI agents with sophisticated data processing capabilities to provide insights into geopolitical situations and cyber threats.

### Key Features

- **🌍 Geopolitical Analysis**: Comprehensive analysis of international relations, regional stability, and political developments
- **🔒 Cyber Threat Intelligence**: IOC extraction, threat actor tracking, and security analysis
- **🤖 AI-Powered Agents**: Specialized agents for different analysis domains
- **🔄 Workflow Automation**: Automated multi-step analysis workflows
- **🛡️ Security & Privacy**: Enterprise-grade encryption and privacy protection
- **📊 Rich Visualizations**: Interactive dashboards and reporting
- **🔌 API Integration**: RESTful API for programmatic access

### System Requirements

- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **Python**: 3.8 or higher
- **Memory**: 8GB RAM minimum, 16GB recommended
- **Storage**: 10GB free space
- **Network**: Internet connection for external data sources

## Getting Started

### Installation

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-org/osint-framework.git
   cd osint-framework
   ```

2. **Set Up Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure Environment Variables**
   
   Create a `.env` file in the project root:
   ```env
   # Required API Keys
   OPENAI_API_KEY=your_openai_api_key_here
   SERPER_API_KEY=your_serper_api_key_here
   
   # Optional Configuration
   OSINT_LOG_LEVEL=INFO
   OSINT_MAX_WORKERS=4
   OSINT_CACHE_TTL=3600
   ```

5. **Initialize the System**
   ```bash
   python setup.py install
   python -m osint_framework init
   ```

### First Run

1. **Start the API Server**
   ```bash
   python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
   ```

2. **Launch the Web Interface**
   ```bash
   streamlit run ui/streamlit_app.py --server.port 8501
   ```

3. **Access the Application**
   - Web Interface: http://localhost:8501
   - API Documentation: http://localhost:8000/docs
   - API Redoc: http://localhost:8000/redoc

### Initial Configuration

1. **Create Admin User**
   
   Navigate to the web interface and create your first admin user account.

2. **Verify API Keys**
   
   The system will automatically verify your API keys and display their status.

3. **Run System Check**
   
   Use the built-in system check to verify all components are working:
   ```bash
   python -m osint_framework check
   ```

## Web Interface Guide

### Dashboard Overview

The main dashboard provides:

- **Quick Stats**: Overview of recent analyses and system status
- **Recent Activities**: Timeline of recent analysis tasks
- **System Health**: Real-time monitoring of system components
- **Quick Actions**: Shortcuts to common analysis tasks

### Navigation

The sidebar contains the main navigation menu:

- **🏠 Home**: Dashboard and overview
- **🌍 Geopolitical Analysis**: International relations and political analysis
- **🔒 Cyber Threat Intelligence**: Security and threat analysis
- **📚 Document Management**: Upload and manage analysis documents
- **📊 Analytics Dashboard**: Detailed reporting and visualizations
- **⚙️ Settings**: System configuration and preferences

### User Interface Elements

#### Analysis Forms

All analysis forms include:

- **Input Validation**: Real-time validation of user inputs
- **Help Text**: Contextual help and examples
- **Progress Indicators**: Visual feedback during analysis
- **Error Handling**: Clear error messages and recovery suggestions

#### Results Display

Analysis results are presented with:

- **Executive Summary**: Key findings and insights
- **Detailed Analysis**: Comprehensive analysis results
- **Visualizations**: Charts, graphs, and interactive maps
- **Export Options**: Download results in various formats
- **Sharing**: Share results with team members

## Geopolitical Analysis

### Intelligence Brief Generation

Generate comprehensive intelligence briefs on geopolitical topics:

1. **Select Analysis Type**: Choose "Intelligence Brief" from the dropdown
2. **Enter Topic**: Provide a specific topic or question
3. **Select Regions**: Choose relevant geographical regions
4. **Set Time Range**: Define the analysis timeframe
5. **Configure Options**: Enable economic and security analysis as needed
6. **Start Analysis**: Click "Generate Intelligence Brief"

**Example Topics:**
- "Middle East diplomatic developments"
- "Eastern European security situation"
- "Asia-Pacific trade relations"
- "African political transitions"

### Regional Monitoring

Set up continuous monitoring of specific regions:

1. **Select Regions**: Choose regions to monitor
2. **Define Keywords**: Enter relevant keywords and topics
3. **Set Frequency**: Choose update frequency (real-time, hourly, daily)
4. **Configure Alerts**: Set up notification preferences
5. **Start Monitoring**: Activate the monitoring system

### Situation Reports

Generate multi-region situation reports:

1. **Select Multiple Regions**: Choose 2-5 regions for comparison
2. **Set Time Frame**: Define the reporting period
3. **Choose Focus Areas**: Select specific areas of interest
4. **Generate Report**: Create comprehensive situation report

### Custom Analysis

Perform specialized geopolitical analysis:

1. **Enter Query**: Provide detailed analysis requirements
2. **Add Context**: Include relevant background information
3. **Set Parameters**: Configure analysis depth and scope
4. **Run Analysis**: Execute custom analysis workflow

## Cyber Threat Intelligence

### IOC Extraction

Extract Indicators of Compromise from threat reports:

1. **Input Method Selection**:
   - **Text Input**: Paste threat report directly
   - **File Upload**: Upload PDF, DOCX, or TXT files
   - **URL Analysis**: Analyze web-based threat reports

2. **Configure Extraction**:
   - Select IOC types (IPs, domains, hashes, emails)
   - Set confidence thresholds
   - Enable additional context extraction

3. **Review Results**:
   - IOCs organized by type
   - Confidence scores and context
   - Export capabilities

### Threat Actor Tracking

Track and analyze threat actor activities:

1. **Enter Actor Name**: Provide threat actor identifier
2. **Select Search Type**:
   - **Known Profile**: Retrieve existing intelligence
   - **Recent Activity**: Latest campaigns and activities
   - **TTPs Analysis**: Tactics, techniques, and procedures
   - **Attribution Research**: Attribution and connections

3. **Review Intelligence**:
   - Actor profile and history
   - Recent campaigns and targets
   - Associated IOCs and tools
   - Attribution confidence

### Campaign Analysis

Analyze threat campaigns and correlate activities:

1. **Input Campaign Data**: Provide campaign information
2. **Set Analysis Scope**: Define temporal and geographical scope
3. **Configure Correlation**: Enable cross-reference with known campaigns
4. **Generate Report**: Create comprehensive campaign analysis

## Workflow Management

### Pre-built Workflows

The system includes several pre-built workflows:

#### Comprehensive Investigation
- **Purpose**: Complete OSINT investigation of a target
- **Parameters**: Target entity, investigation scope
- **Duration**: 15-30 minutes
- **Outputs**: Multi-dimensional analysis report

#### Threat Monitoring Pipeline
- **Purpose**: Continuous threat landscape monitoring
- **Parameters**: Threat categories, update frequency
- **Duration**: Ongoing
- **Outputs**: Real-time threat alerts and reports

#### Social Media Monitoring
- **Purpose**: Monitor social media for specific topics
- **Parameters**: Keywords, platforms, sentiment analysis
- **Duration**: Ongoing
- **Outputs**: Social media intelligence reports

#### Incident Response Workflow
- **Purpose**: Rapid response to security incidents
- **Parameters**: Incident type, IOCs, timeline
- **Duration**: 5-15 minutes
- **Outputs**: Incident analysis and recommendations

### Custom Workflows

Create custom workflows for specific use cases:

1. **Workflow Designer**: Use the visual workflow designer
2. **Task Configuration**: Configure individual workflow tasks
3. **Dependency Management**: Define task dependencies and flow
4. **Testing**: Test workflow with sample data
5. **Deployment**: Deploy workflow for production use

### Workflow Execution

Execute workflows through:

- **Web Interface**: Manual execution with parameter input
- **API Calls**: Programmatic execution via REST API
- **Scheduled Execution**: Automated execution on schedule
- **Event Triggers**: Execution triggered by external events

## Security Features

### Data Encryption

All sensitive data is encrypted using:

- **AES-256 Encryption**: Industry-standard encryption
- **Key Management**: Secure key generation and rotation
- **At-Rest Encryption**: Database and file encryption
- **In-Transit Encryption**: HTTPS/TLS for all communications

### Access Control

Comprehensive access control system:

- **Role-Based Access**: Granular permission system
- **Multi-Factor Authentication**: Optional 2FA/MFA
- **Session Management**: Secure session handling
- **Audit Logging**: Complete audit trail

### Privacy Protection

Privacy-preserving features:

- **Local Processing**: Option for local-only analysis
- **Data Anonymization**: Automatic PII detection and removal
- **Retention Policies**: Configurable data retention
- **Secure Deletion**: Cryptographic data erasure

## Best Practices

### Analysis Quality

- **Verify Sources**: Always verify information from multiple sources
- **Context Awareness**: Consider historical and cultural context
- **Bias Recognition**: Be aware of potential biases in sources
- **Confidence Assessment**: Evaluate confidence levels of findings

### Security Practices

- **Regular Updates**: Keep the system and dependencies updated
- **Key Rotation**: Regularly rotate API keys and encryption keys
- **Access Reviews**: Periodically review user access and permissions
- **Backup Strategy**: Implement comprehensive backup procedures

### Performance Optimization

- **Resource Monitoring**: Monitor system resource usage
- **Cache Management**: Optimize caching for frequently accessed data
- **Parallel Processing**: Utilize parallel processing for large analyses
- **Database Optimization**: Regular database maintenance and optimization

### Data Management

- **Quality Control**: Implement data quality checks
- **Version Control**: Track changes to analysis configurations
- **Documentation**: Document analysis methodologies and findings
- **Archival**: Implement proper data archival procedures

## Troubleshooting

### Common Issues

#### API Key Errors
**Problem**: "API keys not configured" error
**Solution**: 
1. Verify `.env` file exists and contains valid API keys
2. Restart the application after updating keys
3. Check API key format and permissions

#### Memory Issues
**Problem**: Out of memory errors during analysis
**Solution**:
1. Increase system memory allocation
2. Reduce analysis scope or batch size
3. Enable disk-based caching
4. Optimize workflow parameters

#### Network Connectivity
**Problem**: External data source timeouts
**Solution**:
1. Check internet connectivity
2. Verify firewall and proxy settings
3. Increase timeout values in configuration
4. Use alternative data sources

#### Performance Issues
**Problem**: Slow analysis execution
**Solution**:
1. Monitor system resources
2. Optimize database queries
3. Enable parallel processing
4. Review workflow efficiency

### Getting Help

- **Documentation**: Comprehensive documentation in `/docs`
- **API Reference**: Interactive API documentation at `/docs`
- **Community Forum**: Join the community discussion
- **Issue Tracker**: Report bugs and feature requests
- **Professional Support**: Enterprise support options available

### Log Analysis

System logs are available in:
- **Application Logs**: `/logs/osint_framework.log`
- **API Logs**: `/logs/api.log`
- **Error Logs**: `/logs/errors.log`
- **Audit Logs**: `/logs/audit.log`

Use log analysis tools to troubleshoot issues:
```bash
# View recent errors
tail -f logs/errors.log

# Search for specific issues
grep "ERROR" logs/osint_framework.log

# Analyze API performance
grep "slow_query" logs/api.log
```

### System Monitoring

Monitor system health using:
- **Health Check Endpoint**: `/api/v1/health`
- **Metrics Dashboard**: Built-in monitoring dashboard
- **External Monitoring**: Integration with monitoring tools
- **Alerting**: Configurable alert thresholds

## Advanced Features

### Batch Processing

Process multiple analyses simultaneously:

1. **Batch Upload**: Upload multiple files or URLs
2. **Queue Management**: Monitor processing queue
3. **Parallel Execution**: Automatic parallel processing
4. **Results Aggregation**: Combined results and reporting

### Integration Capabilities

- **Webhook Integration**: Real-time notifications
- **API Integration**: Connect with external systems
- **Database Connectors**: Direct database integration
- **Export Formats**: Multiple export formats (JSON, CSV, PDF, XML)

### Customization Options

- **Custom Agents**: Develop specialized analysis agents
- **Plugin System**: Extend functionality with plugins
- **Theme Customization**: Customize UI appearance
- **Branding**: White-label deployment options
