"""
🧠 CrewAI OSINT Agent Framework - Workflow Examples

Comprehensive examples demonstrating advanced workflow orchestration
capabilities for various OSINT scenarios.
"""

import asyncio
import json
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from workflows.workflow_engine import workflow_engine
from workflows.workflow_builder import WorkflowBuilder, WorkflowTemplates
from workflows.scheduler import workflow_scheduler, TriggerType
from utils.logging_config import osint_logger


async def example_comprehensive_investigation():
    """Example: Comprehensive target investigation workflow"""
    
    print("🔍 Creating Comprehensive Investigation Workflow")
    
    # Create workflow using template
    target = "example-target.com"
    builder = WorkflowTemplates.comprehensive_target_investigation(target)
    
    # Register workflow
    workflow_id = builder.register()
    print(f"✅ Workflow registered: {workflow_id}")
    
    # Execute workflow
    execution_id = await workflow_engine.execute_workflow(
        workflow_id=workflow_id,
        initial_context={
            "investigation_priority": "high",
            "analyst": "automated_system"
        }
    )
    
    print(f"🚀 Workflow execution started: {execution_id}")
    
    # Monitor execution
    await monitor_execution(execution_id)
    
    return workflow_id, execution_id


async def example_threat_monitoring_pipeline():
    """Example: Automated threat monitoring pipeline"""
    
    print("🛡️ Creating Threat Monitoring Pipeline")
    
    # Create workflow using template
    builder = WorkflowTemplates.threat_monitoring_pipeline()
    
    # Register workflow
    workflow_id = builder.register()
    print(f"✅ Workflow registered: {workflow_id}")
    
    # Schedule for continuous monitoring (every hour)
    schedule_id = workflow_scheduler.schedule_workflow(
        workflow_id=workflow_id,
        trigger_type=TriggerType.SCHEDULE,
        schedule="0 * * * *",  # Every hour
        context={
            "monitoring_type": "continuous",
            "alert_threshold": "medium"
        },
        max_executions=24  # Run for 24 hours
    )
    
    print(f"⏰ Workflow scheduled: {schedule_id}")
    
    return workflow_id, schedule_id


async def example_event_driven_workflow():
    """Example: Event-driven incident response workflow"""
    
    print("⚡ Creating Event-Driven Incident Response")
    
    # Create incident response workflow
    builder = WorkflowTemplates.incident_response_workflow(
        incident_type="malware_detection",
        indicators=["suspicious-domain.com", "************0", "malware.exe"]
    )
    
    # Register workflow
    workflow_id = builder.register()
    print(f"✅ Workflow registered: {workflow_id}")
    
    # Schedule for event-driven execution
    schedule_id = workflow_scheduler.schedule_workflow(
        workflow_id=workflow_id,
        trigger_type=TriggerType.EVENT,
        event_type="security_alert",
        context={
            "response_team": "soc_team_1",
            "escalation_level": "automatic"
        }
    )
    
    print(f"📡 Event listener registered: {schedule_id}")
    
    # Simulate security alert event
    await asyncio.sleep(2)
    await workflow_scheduler.trigger_event("security_alert", {
        "alert_id": "ALT-2024-001",
        "severity": "high",
        "source": "automated_detection"
    })
    
    print("🚨 Security alert event triggered")
    
    return workflow_id, schedule_id


async def example_custom_workflow():
    """Example: Custom workflow with complex logic"""
    
    print("🔧 Creating Custom Complex Workflow")
    
    # Build custom workflow
    builder = WorkflowBuilder(
        name="Advanced Multi-Stage Investigation",
        description="Complex workflow with conditional logic and parallel processing"
    )
    
    # Stage 1: Initial reconnaissance
    builder.add_tool_task(
        task_id="initial_search",
        name="Initial Web Search",
        tool_name="serper_search",
        parameters={
            "query": "advanced persistent threat APT",
            "search_type": "search"
        }
    )
    
    # Stage 2: Parallel agent analysis
    builder.add_agent_task(
        task_id="geo_analysis",
        name="Geopolitical Analysis",
        agent_type="geopolitical",
        parameters={
            "query": "APT geopolitical implications",
            "regions": ["Global", "Asia", "Europe"],
            "analysis_type": "intelligence_brief"
        },
        dependencies=["initial_search"]
    )
    
    builder.add_agent_task(
        task_id="cti_analysis",
        name="Threat Intelligence Analysis",
        agent_type="cti",
        parameters={
            "content": "APT threat intelligence analysis",
            "analysis_type": "threat_actor_tracking"
        },
        dependencies=["initial_search"]
    )
    
    # Stage 3: Conditional deep dive
    builder.add_condition_task(
        task_id="check_threat_level",
        name="Assess Threat Level",
        condition={
            "type": "or",
            "conditions": [
                {
                    "type": "contains",
                    "left": {"task_result": "geo_analysis", "path": ["result", "analysis"]},
                    "right": "high risk"
                },
                {
                    "type": "contains",
                    "left": {"task_result": "cti_analysis", "path": ["result", "threat_indicators"]},
                    "right": "critical"
                }
            ]
        },
        dependencies=["geo_analysis", "cti_analysis"]
    )
    
    # Stage 4: Deep investigation (conditional)
    builder.add_agent_task(
        task_id="deep_investigation",
        name="Deep Browser Investigation",
        agent_type="browser",
        parameters={
            "target": "apt-research-target.com",
            "investigation_type": "comprehensive",
            "use_stealth": True
        },
        dependencies=["check_threat_level"],
        conditions={
            "if_result": {
                "task_id": "check_threat_level",
                "equals": {"condition_result": True}
            }
        }
    )
    
    # Stage 5: Delay before final report
    builder.add_delay_task(
        task_id="processing_delay",
        name="Processing Delay",
        seconds=5,
        dependencies=["deep_investigation"]
    )
    
    # Stage 6: Final reporting webhook
    builder.add_webhook_task(
        task_id="final_report",
        name="Send Final Report",
        url="https://httpbin.org/post",  # Test endpoint
        method="POST",
        data={
            "report_type": "apt_investigation",
            "classification": "restricted"
        },
        dependencies=["processing_delay"]
    )
    
    # Set workflow configuration
    builder.set_timeout(1800)  # 30 minutes
    builder.set_max_concurrent_executions(2)
    
    # Register workflow
    workflow_id = builder.register()
    print(f"✅ Custom workflow registered: {workflow_id}")
    
    # Execute workflow
    execution_id = await workflow_engine.execute_workflow(
        workflow_id=workflow_id,
        initial_context={
            "investigation_id": "INV-2024-001",
            "analyst": "senior_analyst",
            "priority": "high"
        }
    )
    
    print(f"🚀 Custom workflow execution started: {execution_id}")
    
    # Monitor execution
    await monitor_execution(execution_id)
    
    return workflow_id, execution_id


async def example_batch_processing():
    """Example: Batch domain analysis workflow"""
    
    print("📦 Creating Batch Processing Workflow")
    
    # List of domains to analyze
    domains = [
        "example1.com",
        "example2.com", 
        "example3.com",
        "test-domain.org",
        "sample-site.net"
    ]
    
    # Create batch workflow
    builder = WorkflowTemplates.batch_domain_analysis(domains)
    
    # Register workflow
    workflow_id = builder.register()
    print(f"✅ Batch workflow registered: {workflow_id}")
    
    # Execute workflow
    execution_id = await workflow_engine.execute_workflow(
        workflow_id=workflow_id,
        initial_context={
            "batch_id": "BATCH-2024-001",
            "analysis_depth": "standard"
        }
    )
    
    print(f"🚀 Batch workflow execution started: {execution_id}")
    
    # Monitor execution
    await monitor_execution(execution_id)
    
    return workflow_id, execution_id


async def monitor_execution(execution_id: str, max_wait_time: int = 300):
    """Monitor workflow execution progress"""
    
    print(f"👀 Monitoring execution: {execution_id}")
    
    start_time = datetime.now()
    
    while True:
        execution = workflow_engine.get_workflow_status(execution_id)
        
        if not execution:
            print("❌ Execution not found")
            break
        
        print(f"📊 Status: {execution.status.value}")
        
        if execution.status.value in ["completed", "failed", "cancelled"]:
            print(f"✅ Execution finished: {execution.status.value}")
            
            if execution.error_message:
                print(f"❌ Error: {execution.error_message}")
            
            # Print task results summary
            if execution.task_results:
                print(f"📋 Task Results ({len(execution.task_results)} tasks):")
                for task_id, result in execution.task_results.items():
                    if isinstance(result, dict):
                        status = result.get("status", "unknown")
                        print(f"  - {task_id}: {status}")
                    else:
                        print(f"  - {task_id}: {type(result).__name__}")
            
            break
        
        # Check timeout
        elapsed = (datetime.now() - start_time).total_seconds()
        if elapsed > max_wait_time:
            print(f"⏰ Monitoring timeout after {max_wait_time}s")
            break
        
        await asyncio.sleep(10)  # Check every 10 seconds


async def demonstrate_scheduler():
    """Demonstrate scheduler capabilities"""
    
    print("⏰ Starting Workflow Scheduler")
    
    # Start the scheduler
    await workflow_scheduler.start()
    
    # Create and schedule some workflows
    await example_threat_monitoring_pipeline()
    await example_event_driven_workflow()
    
    # Show scheduler stats
    stats = workflow_scheduler.get_schedule_stats()
    print(f"📊 Scheduler Stats: {json.dumps(stats, indent=2)}")
    
    # Let scheduler run for a bit
    print("⏳ Letting scheduler run for 30 seconds...")
    await asyncio.sleep(30)
    
    # Stop scheduler
    await workflow_scheduler.stop()
    print("🛑 Scheduler stopped")


async def main():
    """Main example runner"""
    
    print("🧠 CrewAI OSINT Framework - Workflow Examples")
    print("=" * 60)
    
    try:
        # Run examples
        print("\n1. Comprehensive Investigation Example")
        await example_comprehensive_investigation()
        
        print("\n2. Custom Complex Workflow Example")
        await example_custom_workflow()
        
        print("\n3. Batch Processing Example")
        await example_batch_processing()
        
        print("\n4. Scheduler Demonstration")
        await demonstrate_scheduler()
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"❌ Example failed: {e}")
        osint_logger.log_error(e, "workflow_examples")


if __name__ == "__main__":
    asyncio.run(main())
