[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "osint-framework"
version = "1.0.0"
description = "🧠 CrewAI OSINT Agent Framework - Advanced multi-agent OSINT platform"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "OSINT Framework Team", email = "<EMAIL>"}
]
keywords = ["osint", "intelligence", "crewai", "agents", "security"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Information Technology",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Security",
    "Topic :: Scientific/Engineering :: Information Analysis",
]
requires-python = ">=3.9"
dependencies = [
    "crewai",
    "langchain",
    "langchain-community",
    "langchain-openai",
    "langgraph",
    "llama-index",
    "llama-index-embeddings-openai",
    "llama-index-llms-openai",
    "dspy-ai",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "selenium>=4.15.0",
    "playwright>=1.40.0",
    "undetected-chromedriver>=3.5.0",
    "crawl4ai>=0.6.3",  # Note: Has SSRF vulnerability CVE-2025-28197, use with caution
    "opencv-python>=4.8.0",
    "pillow>=10.0.0",
    "pytesseract>=0.3.10",
    "easyocr>=1.7.0",
    "face-recognition>=1.3.0",
    "cryptography>=41.0.0",
    "pyjwt>=2.8.0",
    "sentence-transformers>=2.2.0",
    "structlog>=23.1.0",
    "prometheus-client>=0.17.0",
    "psutil>=5.9.0",
    "pydantic>=2.4.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.1",
    "httpx>=0.25.0",
    "rich>=13.6.0",
    "click>=8.1.0",
    "pyyaml>=6.0.1",
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-benchmark>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pylint>=2.17.0",
    "bandit>=1.7.0",
    "safety>=2.3.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocs-mermaid2-plugin>=1.1.0",
]

[project.urls]
Homepage = "https://github.com/osint-framework/osint-framework"
Documentation = "https://osint-framework.github.io/osint-framework"
Repository = "https://github.com/osint-framework/osint-framework"
Issues = "https://github.com/osint-framework/osint-framework/issues"

[project.scripts]
osint-framework = "scripts.run_framework:main"
osint-test = "scripts.run_tests:main"
osint-monitor = "scripts.monitoring_cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["agents*", "tools*", "workflows*", "rag*", "utils*", "api*", "scripts*"]
exclude = ["tests*", "docs*", ".github*"]

[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | node_modules
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
src_paths = ["agents", "tools", "workflows", "rag", "utils", "api", "scripts", "tests"]

[tool.flake8]
max-line-length = 100
extend-ignore = ["E203", "E266", "E501", "W503", "F403", "F401"]
max-complexity = 10
select = ["B", "C", "E", "F", "W", "T4", "B9"]
exclude = [
    ".git",
    "__pycache__",
    "docs/source/conf.py",
    "old",
    "build",
    "dist",
    ".venv",
    "venv",
    ".eggs",
    "*.egg",
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "crewai.*",
    "langchain.*",
    "llama_index.*",
    "dspy.*",
    "selenium.*",
    "playwright.*",
    "cv2.*",
    "face_recognition.*",
    "easyocr.*",
    "pytesseract.*",
    "undetected_chromedriver.*",
]
ignore_missing_imports = true

[tool.pylint]
max-line-length = 100
disable = [
    "C0103",  # Invalid name
    "C0114",  # Missing module docstring
    "C0115",  # Missing class docstring
    "C0116",  # Missing function docstring
    "R0903",  # Too few public methods
    "R0913",  # Too many arguments
    "W0613",  # Unused argument
    "W0622",  # Redefined builtin
]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=agents",
    "--cov=tools", 
    "--cov=workflows",
    "--cov=rag",
    "--cov=utils",
    "--cov=api",
    "--cov-report=term-missing",
    "--cov-report=xml",
    "--cov-report=html",
]
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "api: marks tests that require API keys",
    "browser: marks tests that require browser automation",
    "multimodal: marks tests that require multimodal dependencies",
]
asyncio_mode = "auto"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["agents", "tools", "workflows", "rag", "utils", "api"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "docs", ".venv", "venv"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection for tests

[tool.bandit.assert_used]
skips = ["*_test.py", "test_*.py"]
