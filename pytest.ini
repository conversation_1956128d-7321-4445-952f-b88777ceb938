[tool:pytest]
# Pytest configuration for OSINT Framework testing

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=agents
    --cov=tools
    --cov=workflows
    --cov=api
    --cov=utils
    --cov=rag
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --durations=10
    --maxfail=5

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    security: Security-related tests
    performance: Performance tests
    api: API tests
    agents: Agent tests
    workflows: Workflow tests
    tools: Tool tests
    utils: Utility tests
    rag: RAG system tests
    ui: UI tests
    smoke: Smoke tests for basic functionality
    regression: Regression tests
    external: Tests requiring external services

# Test timeout
timeout = 300

# Asyncio mode
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:streamlit.*
    ignore::UserWarning:plotly.*

# Log configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Environment variables for testing
env =
    OSINT_TEST_MODE=true
    OSINT_LOG_LEVEL=DEBUG
    PYTHONPATH=.
