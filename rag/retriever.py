"""
Document retriever for advanced search and filtering capabilities.

This module provides sophisticated document retrieval functionality with support for
multiple search strategies, advanced filtering, ranking algorithms, and performance
optimization. It includes semantic search, keyword search, hybrid search, and
time-based filtering capabilities.
"""

import asyncio
import logging
import re
from collections import Counter, defaultdict
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from llama_index.core import QueryBundle, VectorStoreIndex
from llama_index.core.postprocessor import KeywordNodePostprocessor, SimilarityPostprocessor
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.response_synthesizers import ResponseMode
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.schema import BaseNode, NodeWithScore


class DocumentRetriever:
    """
    Advanced document retriever with comprehensive search and filtering capabilities.

    This class provides sophisticated search functionality with multiple strategies:
    - Semantic similarity search with configurable thresholds
    - Keyword-based search with fuzzy matching
    - Metadata-based filtering with complex conditions
    - Time-based filtering with flexible date ranges
    - Hybrid search combining multiple strategies with weighted scoring
    - Advanced ranking algorithms and result diversification
    - Performance optimization with caching and parallel processing

    Attributes:
        index: VectorStoreIndex for document storage and retrieval
        base_retriever: Core retriever for semantic search
        logger: Logger instance for debugging and monitoring
        search_cache: Cache for frequently accessed search results
        performance_stats: Statistics for monitoring retrieval performance
    """

    def __init__(
        self,
        index: VectorStoreIndex,
        default_top_k: int = 20,
        enable_caching: bool = True,
        cache_ttl: int = 3600,  # 1 hour
    ) -> None:
        """
        Initialize the document retriever with advanced configuration.

        Args:
            index: VectorStoreIndex to search against
            default_top_k: Default number of results to retrieve initially
            enable_caching: Enable result caching for performance
            cache_ttl: Cache time-to-live in seconds

        Raises:
            ValueError: If index is invalid or empty
        """
        if not index or not hasattr(index, "docstore"):
            raise ValueError("Invalid or empty index provided")

        self.index = index
        self.default_top_k = max(default_top_k, 1)
        self.enable_caching = enable_caching
        self.cache_ttl = cache_ttl

        self.logger = logging.getLogger(__name__)

        # Initialize base retriever with optimized settings
        self.base_retriever = VectorIndexRetriever(index=index, similarity_top_k=self.default_top_k)

        # Initialize caching if enabled
        self.search_cache: Dict[str, Dict[str, Any]] = {} if enable_caching else None

        # Performance tracking
        self.performance_stats = {
            "total_searches": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "average_search_time": 0.0,
            "total_search_time": 0.0,
        }

        # Document statistics
        self._document_stats = self._calculate_document_stats()

        self.logger.info(
            f"DocumentRetriever initialized with {self._document_stats['total_documents']} documents"
        )

    def _calculate_document_stats(self) -> Dict[str, Any]:
        """Calculate statistics about the document collection."""
        try:
            docs = self.index.docstore.docs

            stats = {
                "total_documents": len(docs),
                "source_types": defaultdict(int),
                "total_content_length": 0,
                "average_content_length": 0,
            }

            for doc_id, doc in docs.items():
                if hasattr(doc, "metadata") and doc.metadata:
                    source_type = doc.metadata.get("source_type", "unknown")
                    stats["source_types"][source_type] += 1

                if hasattr(doc, "text") and doc.text:
                    stats["total_content_length"] += len(doc.text)

            if stats["total_documents"] > 0:
                stats["average_content_length"] = (
                    stats["total_content_length"] / stats["total_documents"]
                )

            return dict(stats)

        except Exception as e:
            self.logger.warning(f"Failed to calculate document stats: {str(e)}")
            return {"total_documents": 0, "error": str(e)}

    def semantic_search(
        self,
        query: str,
        top_k: int = 10,
        similarity_threshold: float = 0.7,
        metadata_filters: Optional[Dict[str, Any]] = None,
        enable_reranking: bool = True,
        diversify_results: bool = False,
    ) -> List[Dict[str, Any]]:
        """
        Perform semantic similarity search with advanced features.

        Args:
            query: Search query string
            top_k: Number of results to return (1-100)
            similarity_threshold: Minimum similarity score (0.0-1.0)
            metadata_filters: Optional metadata-based filters
            enable_reranking: Apply advanced reranking algorithms
            diversify_results: Ensure result diversity to avoid redundancy

        Returns:
            List of search results with comprehensive metadata

        Raises:
            ValueError: If parameters are invalid
        """
        start_time = datetime.now()

        # Validate parameters
        if not query or not query.strip():
            raise ValueError("Query cannot be empty")

        top_k = max(1, min(top_k, 100))  # Clamp to reasonable range
        similarity_threshold = max(0.0, min(similarity_threshold, 1.0))

        try:
            # Check cache first
            cache_key = self._generate_cache_key(
                "semantic", query, top_k, similarity_threshold, metadata_filters
            )
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                return cached_result

            self.performance_stats["cache_misses"] += 1

            # Create query bundle with enhanced processing
            query_bundle = QueryBundle(query_str=query.strip())

            # Retrieve more nodes initially for better filtering and reranking
            retrieval_top_k = min(top_k * 3, self.default_top_k)
            self.base_retriever.similarity_top_k = retrieval_top_k

            # Retrieve nodes
            nodes = self.base_retriever.retrieve(query_bundle)

            # Process and filter results
            results = self._process_semantic_nodes(
                nodes, query, similarity_threshold, metadata_filters
            )

            # Apply reranking if enabled
            if enable_reranking and len(results) > 1:
                results = self._rerank_results(results, query)

            # Apply diversification if enabled
            if diversify_results and len(results) > top_k:
                results = self._diversify_results(results, top_k)

            # Limit final results
            final_results = results[:top_k]

            # Add search metadata
            for result in final_results:
                result["search_metadata"] = {
                    "search_type": "semantic",
                    "query": query,
                    "similarity_threshold": similarity_threshold,
                    "timestamp": start_time.isoformat(),
                }

            # Cache results
            self._cache_result(cache_key, final_results)

            # Update performance stats
            search_time = (datetime.now() - start_time).total_seconds()
            self._update_performance_stats(search_time)

            self.logger.info(
                f"Semantic search completed: {len(final_results)} results in {search_time:.3f}s"
            )
            return final_results

        except Exception as e:
            self.logger.error(f"Error in semantic search: {str(e)}")
            return []

    def _process_semantic_nodes(
        self,
        nodes: List[NodeWithScore],
        query: str,
        similarity_threshold: float,
        metadata_filters: Optional[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """Process and filter semantic search nodes."""
        results = []

        for node in nodes:
            try:
                # Extract score safely
                score = (
                    float(node.score) if hasattr(node, "score") and node.score is not None else 0.0
                )

                # Check similarity threshold
                if score < similarity_threshold:
                    continue

                # Apply metadata filters
                if metadata_filters and not self._matches_metadata_filters(
                    node.node.metadata, metadata_filters
                ):
                    continue

                # Extract content and metadata
                content = node.node.text if hasattr(node.node, "text") else ""
                metadata = node.node.metadata if hasattr(node.node, "metadata") else {}
                node_id = node.node.node_id if hasattr(node.node, "node_id") else None

                # Calculate additional relevance metrics
                relevance_metrics = self._calculate_relevance_metrics(content, query)

                result = {
                    "content": content,
                    "score": score,
                    "metadata": metadata,
                    "node_id": node_id,
                    "relevance_metrics": relevance_metrics,
                    "content_length": len(content),
                    "word_count": len(content.split()) if content else 0,
                }

                results.append(result)

            except Exception as e:
                self.logger.warning(f"Error processing node: {str(e)}")
                continue

        # Sort by score
        results.sort(key=lambda x: x["score"], reverse=True)
        return results

    def _calculate_relevance_metrics(self, content: str, query: str) -> Dict[str, float]:
        """Calculate additional relevance metrics for content."""
        if not content or not query:
            return {"query_coverage": 0.0, "term_frequency": 0.0}

        content_lower = content.lower()
        query_lower = query.lower()
        query_terms = set(query_lower.split())
        content_terms = content_lower.split()

        # Calculate query coverage (how many query terms appear in content)
        matching_terms = sum(1 for term in query_terms if term in content_lower)
        query_coverage = matching_terms / len(query_terms) if query_terms else 0.0

        # Calculate term frequency
        term_frequency = (
            sum(content_terms.count(term) for term in query_terms) / len(content_terms)
            if content_terms
            else 0.0
        )

        return {
            "query_coverage": round(query_coverage, 3),
            "term_frequency": round(term_frequency, 4),
        }

    def keyword_search(
        self,
        keywords: List[str],
        top_k: int = 10,
        require_all: bool = False,
        metadata_filters: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """
        Perform keyword-based search.

        Args:
            keywords: List of keywords to search for
            top_k: Number of results to return
            require_all: Whether all keywords must be present
            metadata_filters: Optional metadata filters

        Returns:
            List of search results
        """
        try:
            # Create keyword processor
            keyword_processor = KeywordNodePostprocessor(
                keywords=keywords, required_keywords=keywords if require_all else None
            )

            # Get all documents for keyword filtering
            # Use a broad query to get candidates
            query_bundle = QueryBundle(query_str=" ".join(keywords))
            nodes = self.base_retriever.retrieve(query_bundle)

            # Apply keyword filtering
            filtered_nodes = keyword_processor.postprocess_nodes(nodes)

            # Process results
            results = []
            for node in filtered_nodes:
                # Apply metadata filters
                if metadata_filters and not self._matches_metadata_filters(
                    node.node.metadata, metadata_filters
                ):
                    continue

                # Calculate keyword match score
                keyword_score = self._calculate_keyword_score(node.node.text, keywords)

                result = {
                    "content": node.node.text,
                    "score": keyword_score,
                    "metadata": node.node.metadata,
                    "matched_keywords": self._find_matched_keywords(node.node.text, keywords),
                    "node_id": node.node.node_id if hasattr(node.node, "node_id") else None,
                }
                results.append(result)

            # Sort by keyword score and limit results
            results.sort(key=lambda x: x["score"], reverse=True)
            return results[:top_k]

        except Exception as e:
            self.logger.error(f"Error in keyword search: {str(e)}")
            return []

    def hybrid_search(
        self,
        query: str,
        keywords: List[str] = None,
        top_k: int = 10,
        semantic_weight: float = 0.7,
        keyword_weight: float = 0.3,
        metadata_filters: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining semantic and keyword search.

        Args:
            query: Semantic search query
            keywords: Optional keywords for keyword search
            top_k: Number of results to return
            semantic_weight: Weight for semantic scores
            keyword_weight: Weight for keyword scores
            metadata_filters: Optional metadata filters

        Returns:
            List of hybrid search results
        """
        try:
            # Perform semantic search
            semantic_results = self.semantic_search(
                query=query,
                top_k=top_k * 2,  # Get more results for merging
                similarity_threshold=0.5,  # Lower threshold for hybrid
                metadata_filters=metadata_filters,
            )

            # Perform keyword search if keywords provided
            keyword_results = []
            if keywords:
                keyword_results = self.keyword_search(
                    keywords=keywords, top_k=top_k * 2, metadata_filters=metadata_filters
                )

            # Merge and score results
            merged_results = self._merge_search_results(
                semantic_results, keyword_results, semantic_weight, keyword_weight
            )

            # Sort by combined score and limit results
            merged_results.sort(key=lambda x: x["combined_score"], reverse=True)
            return merged_results[:top_k]

        except Exception as e:
            self.logger.error(f"Error in hybrid search: {str(e)}")
            return []

    def time_filtered_search(
        self,
        query: str,
        start_date: datetime = None,
        end_date: datetime = None,
        date_field: str = "created_date",
        top_k: int = 10,
    ) -> List[Dict[str, Any]]:
        """
        Perform search with time-based filtering.

        Args:
            query: Search query
            start_date: Start date for filtering
            end_date: End date for filtering
            date_field: Metadata field containing date information
            top_k: Number of results to return

        Returns:
            List of time-filtered search results
        """
        try:
            # Create time-based metadata filter
            metadata_filters = {}
            if start_date or end_date:
                metadata_filters[date_field] = {"start": start_date, "end": end_date}

            # Perform semantic search with time filters
            results = self.semantic_search(
                query=query, top_k=top_k, metadata_filters=metadata_filters
            )

            return results

        except Exception as e:
            self.logger.error(f"Error in time-filtered search: {str(e)}")
            return []

    def get_similar_documents(
        self, document_id: str, top_k: int = 10, similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Find documents similar to a given document.

        Args:
            document_id: ID of the reference document
            top_k: Number of similar documents to return
            similarity_threshold: Minimum similarity score

        Returns:
            List of similar documents
        """
        try:
            # Get the reference document
            ref_doc = self.index.docstore.get_document(document_id)
            if not ref_doc:
                self.logger.warning(f"Document not found: {document_id}")
                return []

            # Use document text as query for similarity search
            query = ref_doc.text[:1000]  # Use first 1000 chars as query

            results = self.semantic_search(
                query=query,
                top_k=top_k + 1,  # +1 to account for the reference doc itself
                similarity_threshold=similarity_threshold,
            )

            # Remove the reference document from results
            filtered_results = [
                result for result in results if result.get("node_id") != document_id
            ]

            return filtered_results[:top_k]

        except Exception as e:
            self.logger.error(f"Error finding similar documents: {str(e)}")
            return []

    def _matches_metadata_filters(self, metadata: Dict[str, Any], filters: Dict[str, Any]) -> bool:
        """Check if metadata matches the provided filters."""
        for key, filter_value in filters.items():
            if key not in metadata:
                return False

            metadata_value = metadata[key]

            # Handle different filter types
            if isinstance(filter_value, dict):
                # Range filter (e.g., for dates)
                if "start" in filter_value or "end" in filter_value:
                    if not self._matches_range_filter(metadata_value, filter_value):
                        return False
            elif isinstance(filter_value, list):
                # List filter (value must be in list)
                if metadata_value not in filter_value:
                    return False
            else:
                # Exact match filter
                if metadata_value != filter_value:
                    return False

        return True

    def _matches_range_filter(self, value: Any, range_filter: Dict[str, Any]) -> bool:
        """Check if a value matches a range filter."""
        try:
            # Convert value to datetime if it's a string
            if isinstance(value, str):
                value = datetime.fromisoformat(value.replace("Z", "+00:00"))

            start_date = range_filter.get("start")
            end_date = range_filter.get("end")

            if start_date and value < start_date:
                return False
            if end_date and value > end_date:
                return False

            return True

        except Exception:
            return False

    def _calculate_keyword_score(self, text: str, keywords: List[str]) -> float:
        """Calculate keyword match score for a text."""
        text_lower = text.lower()
        total_matches = 0

        for keyword in keywords:
            keyword_lower = keyword.lower()
            matches = text_lower.count(keyword_lower)
            total_matches += matches

        # Normalize by text length and number of keywords
        word_count = len(text.split())
        if word_count == 0:
            return 0.0

        score = (total_matches / word_count) * len(keywords)
        return min(score, 1.0)  # Cap at 1.0

    def _find_matched_keywords(self, text: str, keywords: List[str]) -> List[str]:
        """Find which keywords are present in the text."""
        text_lower = text.lower()
        matched = []

        for keyword in keywords:
            if keyword.lower() in text_lower:
                matched.append(keyword)

        return matched

    def _merge_search_results(
        self,
        semantic_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        semantic_weight: float,
        keyword_weight: float,
    ) -> List[Dict[str, Any]]:
        """Merge semantic and keyword search results."""
        # Create a map of node_id to results for deduplication
        result_map = {}

        # Add semantic results
        for result in semantic_results:
            node_id = result.get("node_id", result["content"][:50])
            result_map[node_id] = {
                **result,
                "semantic_score": result["score"],
                "keyword_score": 0.0,
                "combined_score": result["score"] * semantic_weight,
            }

        # Add/merge keyword results
        for result in keyword_results:
            node_id = result.get("node_id", result["content"][:50])

            if node_id in result_map:
                # Update existing result
                result_map[node_id]["keyword_score"] = result["score"]
                result_map[node_id]["combined_score"] = (
                    result_map[node_id]["semantic_score"] * semantic_weight
                    + result["score"] * keyword_weight
                )
                if "matched_keywords" in result:
                    result_map[node_id]["matched_keywords"] = result["matched_keywords"]
            else:
                # Add new result
                result_map[node_id] = {
                    **result,
                    "semantic_score": 0.0,
                    "keyword_score": result["score"],
                    "combined_score": result["score"] * keyword_weight,
                }

        return list(result_map.values())

    # Caching and performance methods
    def _generate_cache_key(
        self,
        search_type: str,
        query: str,
        top_k: int,
        threshold: float,
        filters: Optional[Dict[str, Any]],
    ) -> str:
        """Generate a cache key for search results."""
        import hashlib

        key_parts = [
            search_type,
            query.strip().lower(),
            str(top_k),
            str(threshold),
            str(sorted(filters.items()) if filters else ""),
        ]

        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _get_cached_result(self, cache_key: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached search result if available and fresh."""
        if not self.enable_caching or not self.search_cache:
            return None

        if cache_key not in self.search_cache:
            return None

        cached_entry = self.search_cache[cache_key]

        # Check if cache entry is still valid
        cache_time = cached_entry.get("timestamp", 0)
        current_time = datetime.now().timestamp()

        if current_time - cache_time > self.cache_ttl:
            # Remove expired entry
            del self.search_cache[cache_key]
            return None

        return cached_entry.get("results")

    def _cache_result(self, cache_key: str, results: List[Dict[str, Any]]) -> None:
        """Cache search results."""
        if not self.enable_caching or not self.search_cache:
            return

        # Limit cache size to prevent memory issues
        max_cache_size = 1000
        if len(self.search_cache) >= max_cache_size:
            # Remove oldest entries
            sorted_entries = sorted(
                self.search_cache.items(), key=lambda x: x[1].get("timestamp", 0)
            )

            # Remove oldest 20% of entries
            entries_to_remove = max_cache_size // 5
            for i in range(entries_to_remove):
                if i < len(sorted_entries):
                    del self.search_cache[sorted_entries[i][0]]

        self.search_cache[cache_key] = {"results": results, "timestamp": datetime.now().timestamp()}

    def _update_performance_stats(self, search_time: float) -> None:
        """Update performance statistics."""
        self.performance_stats["total_searches"] += 1
        self.performance_stats["total_search_time"] += search_time

        # Calculate running average
        total_searches = self.performance_stats["total_searches"]
        self.performance_stats["average_search_time"] = (
            self.performance_stats["total_search_time"] / total_searches
        )

    def _rerank_results(self, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """Apply advanced reranking to improve result quality."""
        if len(results) <= 1:
            return results

        # Calculate reranking scores based on multiple factors
        for result in results:
            rerank_score = self._calculate_rerank_score(result, query)
            result["rerank_score"] = rerank_score

            # Combine original score with rerank score
            original_score = result.get("score", 0.0)
            result["combined_score"] = (original_score * 0.7) + (rerank_score * 0.3)

        # Sort by combined score
        results.sort(key=lambda x: x.get("combined_score", 0.0), reverse=True)
        return results

    def _calculate_rerank_score(self, result: Dict[str, Any], query: str) -> float:
        """Calculate reranking score based on multiple factors."""
        content = result.get("content", "")
        metadata = result.get("metadata", {})

        score_factors = []

        # Content quality factors
        content_length = len(content)
        if 100 <= content_length <= 2000:  # Prefer medium-length content
            score_factors.append(0.8)
        elif content_length < 100:
            score_factors.append(0.4)
        else:
            score_factors.append(0.6)

        # Query term position factor (earlier mentions score higher)
        query_terms = query.lower().split()
        position_scores = []

        for term in query_terms:
            pos = content.lower().find(term)
            if pos != -1:
                # Score based on position (earlier = higher score)
                position_score = max(0.1, 1.0 - (pos / len(content)))
                position_scores.append(position_score)

        if position_scores:
            score_factors.append(sum(position_scores) / len(position_scores))

        # Source type factor
        source_type = metadata.get("source_type", "")
        if source_type in ["url", "file"]:
            score_factors.append(0.9)
        else:
            score_factors.append(0.7)

        # Recency factor (if date available)
        if "processed_at" in metadata:
            try:
                processed_date = datetime.fromisoformat(
                    metadata["processed_at"].replace("Z", "+00:00")
                )
                days_old = (datetime.now() - processed_date).days
                recency_score = max(0.1, 1.0 - (days_old / 365))  # Decay over a year
                score_factors.append(recency_score)
            except:
                pass

        # Return average of all factors
        return sum(score_factors) / len(score_factors) if score_factors else 0.5

    def _diversify_results(
        self, results: List[Dict[str, Any]], target_count: int
    ) -> List[Dict[str, Any]]:
        """Diversify results to avoid redundancy."""
        if len(results) <= target_count:
            return results

        diversified = []
        used_sources = set()
        content_hashes = set()

        # First pass: select diverse results
        for result in results:
            if len(diversified) >= target_count:
                break

            source = result.get("metadata", {}).get("source", "")
            content = result.get("content", "")

            # Simple content hash for similarity detection
            content_hash = hash(content[:200])  # Use first 200 chars

            # Skip if we already have content from this source or very similar content
            if source in used_sources or content_hash in content_hashes:
                continue

            diversified.append(result)
            used_sources.add(source)
            content_hashes.add(content_hash)

        # Second pass: fill remaining slots with best remaining results
        remaining_slots = target_count - len(diversified)
        if remaining_slots > 0:
            remaining_results = [r for r in results if r not in diversified]
            diversified.extend(remaining_results[:remaining_slots])

        return diversified

    def clear_cache(self) -> bool:
        """Clear the search result cache."""
        if self.search_cache:
            self.search_cache.clear()
            self.logger.info("Search cache cleared")
            return True
        return False

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        stats = self.performance_stats.copy()

        # Add cache statistics
        if self.enable_caching and self.search_cache:
            stats["cache_size"] = len(self.search_cache)
            stats["cache_enabled"] = True
        else:
            stats["cache_enabled"] = False

        # Add document statistics
        stats["document_stats"] = self._document_stats

        # Calculate hit rate
        total_requests = stats["cache_hits"] + stats["cache_misses"]
        if total_requests > 0:
            stats["cache_hit_rate"] = round(stats["cache_hits"] / total_requests, 3)

        return stats

    def reset_performance_stats(self) -> None:
        """Reset performance statistics."""
        self.performance_stats = {
            "total_searches": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "average_search_time": 0.0,
            "total_search_time": 0.0,
        }
        self.logger.info("Performance statistics reset")
