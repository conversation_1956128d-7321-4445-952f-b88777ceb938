#!/usr/bin/env python3
"""
🧠 CrewAI OSINT Agent Framework - API Launcher

Simple launcher script for the FastAPI REST API.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Launch the FastAPI REST API"""
    
    # Get the project root directory
    project_root = Path(__file__).parent
    api_app_path = project_root / "api" / "main.py"
    
    # Check if the API app exists
    if not api_app_path.exists():
        print("❌ Error: FastAPI app not found at", api_app_path)
        sys.exit(1)
    
    # Check if fastapi and uvicorn are installed
    try:
        import fastapi
        import uvicorn
    except ImportError:
        print("❌ Error: FastAPI or Uvicorn is not installed.")
        print("Please install them with: pip install fastapi uvicorn")
        sys.exit(1)
    
    # Check for .env file
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  Warning: .env file not found.")
        print("Please create a .env file with your API keys:")
        print("OPENAI_API_KEY=your_openai_key")
        print("SERPER_API_KEY=your_serper_key")
        print()
    
    print("🚀 Starting OSINT Framework API...")
    print(f"📁 Project root: {project_root}")
    print(f"🌐 API will be available at: http://localhost:8000")
    print(f"📚 API docs will be available at: http://localhost:8000/docs")
    print(f"📖 ReDoc will be available at: http://localhost:8000/redoc")
    print()
    
    # Launch FastAPI with Uvicorn
    try:
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "api.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--reload-dir", str(project_root)
        ]
        
        subprocess.run(cmd, cwd=str(project_root))
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down OSINT Framework API...")
    except Exception as e:
        print(f"❌ Error launching API: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
