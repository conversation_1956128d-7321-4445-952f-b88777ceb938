#!/usr/bin/env python3
"""
🧠 CrewAI OSINT Agent Framework - Test Runner

Comprehensive test runner for the OSINT framework with support for different
test types, coverage reporting, and CI/CD integration.

Features:
    - Unit, integration, and end-to-end test execution
    - Coverage reporting with multiple formats
    - Performance benchmarking
    - Security test validation
    - Parallel test execution
    - Test result reporting and analysis
    - CI/CD integration support
"""

import argparse
import os
import subprocess
import sys
import time
from pathlib import Path
from typing import List, Optional

import pytest


class TestRunner:
    """Advanced test runner for the OSINT framework."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_dir = self.project_root / "tests"
        self.coverage_dir = self.project_root / "htmlcov"
        
    def setup_environment(self):
        """Setup test environment and dependencies."""
        print("🔧 Setting up test environment...")
        
        # Ensure test directories exist
        self.test_dir.mkdir(exist_ok=True)
        
        # Set environment variables
        os.environ.update({
            "OSINT_TEST_MODE": "true",
            "OSINT_LOG_LEVEL": "DEBUG",
            "PYTHONPATH": str(self.project_root)
        })
        
        # Install test dependencies if needed
        try:
            import pytest
            import pytest_asyncio
            import pytest_cov
            import pytest_mock
        except ImportError:
            print("📦 Installing test dependencies...")
            subprocess.run([
                sys.executable, "-m", "pip", "install",
                "pytest", "pytest-asyncio", "pytest-cov", "pytest-mock",
                "pytest-xdist", "pytest-timeout", "pytest-benchmark"
            ], check=True)
    
    def run_unit_tests(self, verbose: bool = False, parallel: bool = False) -> int:
        """Run unit tests."""
        print("🧪 Running unit tests...")
        
        args = [
            "-m", "pytest",
            "tests/unit/",
            "-v" if verbose else "-q",
            "--tb=short",
            "--cov=agents,tools,workflows,api,utils,rag",
            "--cov-report=term-missing",
            "--cov-report=html",
            "-m", "unit"
        ]
        
        if parallel:
            args.extend(["-n", "auto"])
        
        return subprocess.run([sys.executable] + args).returncode
    
    def run_integration_tests(self, verbose: bool = False) -> int:
        """Run integration tests."""
        print("🔗 Running integration tests...")
        
        args = [
            "-m", "pytest",
            "tests/integration/",
            "-v" if verbose else "-q",
            "--tb=short",
            "-m", "integration"
        ]
        
        return subprocess.run([sys.executable] + args).returncode
    
    def run_e2e_tests(self, verbose: bool = False) -> int:
        """Run end-to-end tests."""
        print("🎯 Running end-to-end tests...")
        
        args = [
            "-m", "pytest",
            "tests/e2e/",
            "-v" if verbose else "-q",
            "--tb=short",
            "-m", "e2e",
            "--timeout=300"
        ]
        
        return subprocess.run([sys.executable] + args).returncode
    
    def run_security_tests(self, verbose: bool = False) -> int:
        """Run security tests."""
        print("🔒 Running security tests...")
        
        args = [
            "-m", "pytest",
            "tests/",
            "-v" if verbose else "-q",
            "--tb=short",
            "-m", "security"
        ]
        
        return subprocess.run([sys.executable] + args).returncode
    
    def run_performance_tests(self, verbose: bool = False) -> int:
        """Run performance tests."""
        print("⚡ Running performance tests...")
        
        args = [
            "-m", "pytest",
            "tests/",
            "-v" if verbose else "-q",
            "--tb=short",
            "-m", "performance",
            "--benchmark-only"
        ]
        
        return subprocess.run([sys.executable] + args).returncode
    
    def run_all_tests(self, verbose: bool = False, parallel: bool = False) -> int:
        """Run all tests."""
        print("🚀 Running all tests...")
        
        args = [
            "-m", "pytest",
            "tests/",
            "-v" if verbose else "-q",
            "--tb=short",
            "--cov=agents,tools,workflows,api,utils,rag",
            "--cov-report=term-missing",
            "--cov-report=html",
            "--cov-report=xml",
            "--cov-fail-under=80",
            "--durations=10"
        ]
        
        if parallel:
            args.extend(["-n", "auto"])
        
        return subprocess.run([sys.executable] + args).returncode
    
    def run_smoke_tests(self) -> int:
        """Run smoke tests for basic functionality."""
        print("💨 Running smoke tests...")
        
        args = [
            "-m", "pytest",
            "tests/",
            "-q",
            "--tb=line",
            "-m", "smoke",
            "--maxfail=1"
        ]
        
        return subprocess.run([sys.executable] + args).returncode
    
    def generate_coverage_report(self):
        """Generate comprehensive coverage report."""
        print("📊 Generating coverage report...")
        
        # Generate HTML report
        subprocess.run([
            sys.executable, "-m", "coverage", "html",
            "--directory", str(self.coverage_dir)
        ])
        
        # Generate XML report for CI
        subprocess.run([
            sys.executable, "-m", "coverage", "xml"
        ])
        
        # Print coverage summary
        subprocess.run([
            sys.executable, "-m", "coverage", "report"
        ])
        
        print(f"📈 Coverage report generated: {self.coverage_dir}/index.html")
    
    def run_linting(self) -> int:
        """Run code linting and formatting checks."""
        print("🔍 Running code quality checks...")
        
        # Run flake8
        flake8_result = subprocess.run([
            sys.executable, "-m", "flake8",
            "agents/", "tools/", "workflows/", "api/", "utils/", "rag/",
            "--max-line-length=100",
            "--extend-ignore=E203,E266,E501,W503,F403,F401"
        ]).returncode
        
        # Run black check
        black_result = subprocess.run([
            sys.executable, "-m", "black",
            "--check", "--diff",
            "agents/", "tools/", "workflows/", "api/", "utils/", "rag/"
        ]).returncode
        
        # Run isort check
        isort_result = subprocess.run([
            sys.executable, "-m", "isort",
            "--check-only", "--diff",
            "agents/", "tools/", "workflows/", "api/", "utils/", "rag/"
        ]).returncode
        
        return max(flake8_result, black_result, isort_result)
    
    def run_security_scan(self) -> int:
        """Run security vulnerability scan."""
        print("🛡️ Running security scan...")
        
        try:
            # Run safety check
            safety_result = subprocess.run([
                sys.executable, "-m", "safety", "check"
            ]).returncode
            
            # Run bandit security scan
            bandit_result = subprocess.run([
                sys.executable, "-m", "bandit",
                "-r", "agents/", "tools/", "workflows/", "api/", "utils/", "rag/",
                "-f", "json", "-o", "bandit-report.json"
            ]).returncode
            
            return max(safety_result, bandit_result)
            
        except FileNotFoundError:
            print("⚠️ Security tools not installed. Install with: pip install safety bandit")
            return 0


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="OSINT Framework Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py --unit                 # Run unit tests only
  python run_tests.py --integration          # Run integration tests only
  python run_tests.py --all --verbose        # Run all tests with verbose output
  python run_tests.py --smoke                # Run smoke tests
  python run_tests.py --security             # Run security tests
  python run_tests.py --performance          # Run performance tests
  python run_tests.py --lint                 # Run code quality checks
  python run_tests.py --scan                 # Run security scan
        """
    )
    
    # Test type selection
    test_group = parser.add_mutually_exclusive_group()
    test_group.add_argument("--unit", action="store_true", help="Run unit tests")
    test_group.add_argument("--integration", action="store_true", help="Run integration tests")
    test_group.add_argument("--e2e", action="store_true", help="Run end-to-end tests")
    test_group.add_argument("--security", action="store_true", help="Run security tests")
    test_group.add_argument("--performance", action="store_true", help="Run performance tests")
    test_group.add_argument("--smoke", action="store_true", help="Run smoke tests")
    test_group.add_argument("--all", action="store_true", help="Run all tests")
    
    # Additional options
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--parallel", "-p", action="store_true", help="Run tests in parallel")
    parser.add_argument("--coverage", "-c", action="store_true", help="Generate coverage report")
    parser.add_argument("--lint", action="store_true", help="Run code quality checks")
    parser.add_argument("--scan", action="store_true", help="Run security scan")
    parser.add_argument("--setup", action="store_true", help="Setup test environment only")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # Setup environment
    runner.setup_environment()
    
    if args.setup:
        print("✅ Test environment setup complete")
        return 0
    
    start_time = time.time()
    exit_code = 0
    
    try:
        # Run selected tests
        if args.unit:
            exit_code = runner.run_unit_tests(args.verbose, args.parallel)
        elif args.integration:
            exit_code = runner.run_integration_tests(args.verbose)
        elif args.e2e:
            exit_code = runner.run_e2e_tests(args.verbose)
        elif args.security:
            exit_code = runner.run_security_tests(args.verbose)
        elif args.performance:
            exit_code = runner.run_performance_tests(args.verbose)
        elif args.smoke:
            exit_code = runner.run_smoke_tests()
        elif args.all:
            exit_code = runner.run_all_tests(args.verbose, args.parallel)
        elif args.lint:
            exit_code = runner.run_linting()
        elif args.scan:
            exit_code = runner.run_security_scan()
        else:
            # Default: run smoke tests
            exit_code = runner.run_smoke_tests()
        
        # Generate coverage report if requested
        if args.coverage and not args.lint and not args.scan:
            runner.generate_coverage_report()
        
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        exit_code = 130
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        exit_code = 1
    
    # Print summary
    duration = time.time() - start_time
    if exit_code == 0:
        print(f"✅ Tests completed successfully in {duration:.2f}s")
    else:
        print(f"❌ Tests failed with exit code {exit_code} after {duration:.2f}s")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
