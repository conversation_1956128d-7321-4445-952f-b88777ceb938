#!/usr/bin/env python3
"""
🧠 CrewAI OSINT Agent Framework - UI Launcher

Simple launcher script for the Streamlit UI.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Launch the Streamlit UI"""
    
    # Get the project root directory
    project_root = Path(__file__).parent
    ui_app_path = project_root / "ui" / "streamlit_app.py"
    
    # Check if the UI app exists
    if not ui_app_path.exists():
        print("❌ Error: Streamlit app not found at", ui_app_path)
        sys.exit(1)
    
    # Check if streamlit is installed
    try:
        import streamlit
    except ImportError:
        print("❌ Error: Streamlit is not installed.")
        print("Please install it with: pip install streamlit")
        sys.exit(1)
    
    # Check for .env file
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  Warning: .env file not found.")
        print("Please create a .env file with your API keys:")
        print("OPENAI_API_KEY=your_openai_key")
        print("SERPER_API_KEY=your_serper_key")
        print()
    
    print("🚀 Starting OSINT Framework UI...")
    print(f"📁 Project root: {project_root}")
    print(f"🌐 UI will be available at: http://localhost:8501")
    print()
    
    # Launch Streamlit
    try:
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            str(ui_app_path),
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ]
        
        subprocess.run(cmd, cwd=str(project_root))
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down OSINT Framework UI...")
    except Exception as e:
        print(f"❌ Error launching UI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
