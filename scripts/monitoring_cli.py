#!/usr/bin/env python3
"""
🧠 CrewAI OSINT Agent Framework - Monitoring CLI

Command-line interface for monitoring and managing the OSINT framework.
"""

import sys
import os
import json
import time
import asyncio
from pathlib import Path
from datetime import datetime, timedelta
import click
import requests
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.live import Live
from rich.layout import Layout
from rich.text import Text

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.monitoring import health_checker, metrics_collector, alert_manager
from utils.logging_config import osint_logger

console = Console()


@click.group()
def cli():
    """🧠 OSINT Framework Monitoring CLI"""
    pass


@cli.command()
@click.option('--api-url', default='http://localhost:8000', help='API base URL')
def status(api_url):
    """Show current system status"""
    
    try:
        # Get health status
        response = requests.get(f"{api_url}/health", timeout=10)
        health_data = response.json()
        
        # Get metrics
        response = requests.get(f"{api_url}/metrics", timeout=10)
        metrics_data = response.json()
        
        # Display status
        display_status(health_data, metrics_data)
        
    except requests.exceptions.ConnectionError:
        console.print("[red]❌ Cannot connect to API server[/red]")
        console.print(f"Make sure the API is running at {api_url}")
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")


def display_status(health_data, metrics_data):
    """Display system status in a formatted way"""
    
    # Health status
    status_color = "green" if health_data["status"] == "healthy" else "red"
    status_icon = "✅" if health_data["status"] == "healthy" else "❌"
    
    console.print(Panel(
        f"{status_icon} System Status: [{status_color}]{health_data['status'].upper()}[/{status_color}]",
        title="🧠 OSINT Framework Status",
        border_style=status_color
    ))
    
    # Health checks table
    if "checks" in health_data:
        health_table = Table(title="Health Checks")
        health_table.add_column("Check", style="cyan")
        health_table.add_column("Status", style="bold")
        health_table.add_column("Duration", style="magenta")
        
        for check_name, check_data in health_data["checks"].items():
            status_text = check_data["status"]
            status_style = "green" if status_text == "healthy" else "red"
            duration = f"{check_data.get('duration', 0):.3f}s"
            
            health_table.add_row(
                check_name,
                f"[{status_style}]{status_text}[/{status_style}]",
                duration
            )
        
        console.print(health_table)
    
    # System metrics
    if "current" in metrics_data and "system" in metrics_data["current"]:
        system_metrics = metrics_data["current"]["system"]
        
        metrics_table = Table(title="System Metrics")
        metrics_table.add_column("Metric", style="cyan")
        metrics_table.add_column("Value", style="bold")
        metrics_table.add_column("Status", style="bold")
        
        # CPU
        cpu_percent = system_metrics["cpu_percent"]
        cpu_status = "🟢" if cpu_percent < 70 else "🟡" if cpu_percent < 85 else "🔴"
        metrics_table.add_row("CPU Usage", f"{cpu_percent:.1f}%", cpu_status)
        
        # Memory
        memory_percent = system_metrics["memory_percent"]
        memory_status = "🟢" if memory_percent < 75 else "🟡" if memory_percent < 90 else "🔴"
        metrics_table.add_row("Memory Usage", f"{memory_percent:.1f}%", memory_status)
        
        # Disk
        disk_percent = system_metrics["disk_percent"]
        disk_status = "🟢" if disk_percent < 80 else "🟡" if disk_percent < 95 else "🔴"
        metrics_table.add_row("Disk Usage", f"{disk_percent:.1f}%", disk_status)
        
        console.print(metrics_table)


@cli.command()
@click.option('--api-url', default='http://localhost:8000', help='API base URL')
@click.option('--refresh', default=5, help='Refresh interval in seconds')
def monitor(api_url, refresh):
    """Real-time monitoring dashboard"""
    
    def create_dashboard():
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        layout["body"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        return layout
    
    def update_dashboard(layout):
        try:
            # Get data
            health_response = requests.get(f"{api_url}/health", timeout=5)
            metrics_response = requests.get(f"{api_url}/metrics", timeout=5)
            
            health_data = health_response.json()
            metrics_data = metrics_response.json()
            
            # Update header
            status_color = "green" if health_data["status"] == "healthy" else "red"
            layout["header"].update(Panel(
                f"🧠 OSINT Framework - Status: [{status_color}]{health_data['status'].upper()}[/{status_color}] | "
                f"Last Update: {datetime.now().strftime('%H:%M:%S')}",
                style="bold"
            ))
            
            # Update left panel (system metrics)
            if "current" in metrics_data and "system" in metrics_data["current"]:
                system_metrics = metrics_data["current"]["system"]
                
                system_text = Text()
                system_text.append("System Metrics\n\n", style="bold cyan")
                system_text.append(f"CPU: {system_metrics['cpu_percent']:.1f}%\n")
                system_text.append(f"Memory: {system_metrics['memory_percent']:.1f}%\n")
                system_text.append(f"Disk: {system_metrics['disk_percent']:.1f}%\n")
                system_text.append(f"Processes: {system_metrics['active_processes']}\n")
                
                layout["left"].update(Panel(system_text, title="System"))
            
            # Update right panel (health checks)
            if "checks" in health_data:
                health_text = Text()
                health_text.append("Health Checks\n\n", style="bold cyan")
                
                for check_name, check_data in health_data["checks"].items():
                    status = check_data["status"]
                    icon = "✅" if status == "healthy" else "❌"
                    health_text.append(f"{icon} {check_name}: {status}\n")
                
                layout["right"].update(Panel(health_text, title="Health"))
            
            # Update footer
            layout["footer"].update(Panel(
                f"Press Ctrl+C to exit | Refresh every {refresh}s",
                style="dim"
            ))
            
        except Exception as e:
            layout["header"].update(Panel(
                f"❌ Error connecting to API: {e}",
                style="red"
            ))
    
    layout = create_dashboard()
    
    try:
        with Live(layout, refresh_per_second=1/refresh, screen=True):
            while True:
                update_dashboard(layout)
                time.sleep(refresh)
    except KeyboardInterrupt:
        console.print("\n[yellow]Monitoring stopped[/yellow]")


@cli.command()
@click.option('--api-url', default='http://localhost:8000', help='API base URL')
def alerts(api_url):
    """Show current alerts"""
    
    try:
        response = requests.get(f"{api_url}/monitoring/alerts", timeout=10)
        alerts_data = response.json()
        
        # Active alerts
        active_alerts = alerts_data.get("active_alerts", [])
        
        if active_alerts:
            console.print(Panel("🚨 Active Alerts", style="red"))
            
            alerts_table = Table()
            alerts_table.add_column("Alert", style="cyan")
            alerts_table.add_column("Severity", style="bold")
            alerts_table.add_column("Time", style="magenta")
            alerts_table.add_column("Message")
            
            for alert in active_alerts:
                severity_style = "red" if alert["severity"] == "critical" else "yellow"
                alerts_table.add_row(
                    alert["name"],
                    f"[{severity_style}]{alert['severity']}[/{severity_style}]",
                    alert["timestamp"][:19],
                    alert.get("message", "")
                )
            
            console.print(alerts_table)
        else:
            console.print(Panel("✅ No active alerts", style="green"))
        
        # Recent alerts
        alert_history = alerts_data.get("alert_history", [])
        if alert_history:
            console.print(f"\n📊 Recent Alerts ({len(alert_history)} total)")
            
            recent_table = Table()
            recent_table.add_column("Time", style="magenta")
            recent_table.add_column("Alert", style="cyan")
            recent_table.add_column("Severity", style="bold")
            
            for alert in alert_history[-10:]:  # Last 10 alerts
                severity_style = "red" if alert["severity"] == "critical" else "yellow"
                recent_table.add_row(
                    alert["timestamp"][:19],
                    alert["name"],
                    f"[{severity_style}]{alert['severity']}[/{severity_style}]"
                )
            
            console.print(recent_table)
        
    except requests.exceptions.ConnectionError:
        console.print("[red]❌ Cannot connect to API server[/red]")
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")


@cli.command()
@click.option('--hours', default=24, help='Hours of data to analyze')
def performance(hours):
    """Show performance analysis"""
    
    try:
        # Collect current metrics
        current_metrics = metrics_collector.collect_metrics()
        summary = metrics_collector.get_metrics_summary(hours=hours)
        
        console.print(Panel(f"📊 Performance Analysis - Last {hours} hours", style="blue"))
        
        if summary.get("status") == "no_data":
            console.print("[yellow]No performance data available[/yellow]")
            return
        
        # Performance table
        perf_table = Table(title="Performance Metrics")
        perf_table.add_column("Metric", style="cyan")
        perf_table.add_column("Average", style="bold")
        perf_table.add_column("Maximum", style="bold")
        perf_table.add_column("Status", style="bold")
        
        # CPU
        avg_cpu = summary["avg_cpu_percent"]
        max_cpu = summary["max_cpu_percent"]
        cpu_status = "🟢" if avg_cpu < 70 else "🟡" if avg_cpu < 85 else "🔴"
        perf_table.add_row("CPU Usage", f"{avg_cpu:.1f}%", f"{max_cpu:.1f}%", cpu_status)
        
        # Memory
        avg_memory = summary["avg_memory_percent"]
        max_memory = summary["max_memory_percent"]
        memory_status = "🟢" if avg_memory < 75 else "🟡" if avg_memory < 90 else "🔴"
        perf_table.add_row("Memory Usage", f"{avg_memory:.1f}%", f"{max_memory:.1f}%", memory_status)
        
        # Response time
        avg_response = summary["avg_response_time"]
        max_response = summary["max_response_time"]
        response_status = "🟢" if avg_response < 1 else "🟡" if avg_response < 3 else "🔴"
        perf_table.add_row("Response Time", f"{avg_response:.3f}s", f"{max_response:.3f}s", response_status)
        
        console.print(perf_table)
        
        # Data points
        console.print(f"\n📈 Analysis based on {summary['data_points']} data points")
        
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")


@cli.command()
@click.option('--level', default='INFO', help='Log level filter')
@click.option('--lines', default=50, help='Number of lines to show')
def logs(level, lines):
    """Show recent logs"""
    
    try:
        log_file = Path("logs/osint_framework.log")
        
        if not log_file.exists():
            console.print("[yellow]No log file found[/yellow]")
            return
        
        # Read last N lines
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:]
        
        console.print(Panel(f"📝 Recent Logs - Last {len(recent_lines)} lines", style="blue"))
        
        for line in recent_lines:
            line = line.strip()
            if level.upper() in line or level == 'ALL':
                # Color code by log level
                if 'ERROR' in line:
                    console.print(f"[red]{line}[/red]")
                elif 'WARNING' in line:
                    console.print(f"[yellow]{line}[/yellow]")
                elif 'INFO' in line:
                    console.print(f"[blue]{line}[/blue]")
                else:
                    console.print(line)
        
    except Exception as e:
        console.print(f"[red]❌ Error reading logs: {e}[/red]")


@cli.command()
def test():
    """Run monitoring system tests"""
    
    console.print("🧪 Running monitoring system tests...")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        # Test health checks
        task1 = progress.add_task("Testing health checks...", total=None)
        try:
            health_result = asyncio.run(health_checker.run_health_checks())
            if health_result["status"] == "healthy":
                console.print("✅ Health checks: PASSED")
            else:
                console.print("❌ Health checks: FAILED")
                console.print(f"   Critical failures: {health_result['critical_failures']}")
        except Exception as e:
            console.print(f"❌ Health checks: ERROR - {e}")
        progress.remove_task(task1)
        
        # Test metrics collection
        task2 = progress.add_task("Testing metrics collection...", total=None)
        try:
            metrics = metrics_collector.collect_metrics()
            if metrics and "system" in metrics and "application" in metrics:
                console.print("✅ Metrics collection: PASSED")
            else:
                console.print("❌ Metrics collection: FAILED")
        except Exception as e:
            console.print(f"❌ Metrics collection: ERROR - {e}")
        progress.remove_task(task2)
        
        # Test logging
        task3 = progress.add_task("Testing logging system...", total=None)
        try:
            osint_logger.logger.info("Test log message from monitoring CLI")
            console.print("✅ Logging system: PASSED")
        except Exception as e:
            console.print(f"❌ Logging system: ERROR - {e}")
        progress.remove_task(task3)
    
    console.print("\n🎉 Monitoring system tests completed!")


if __name__ == "__main__":
    cli()
