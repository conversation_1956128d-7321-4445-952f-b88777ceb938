#!/usr/bin/env python3
"""
🧠 CrewAI OSINT Agent Framework - Test Runner

Automated test runner for continuous integration and quality assurance.
Supports various test modes and reporting formats.
"""

import asyncio
import argparse
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tests.test_framework import test_framework, OSINTTestFramework
from tests.test_agents import agent_test_suite, multimodal_test_suite, browser_test_suite
from tests.test_tools import tools_test_suite, security_test_suite, monitoring_test_suite
from utils.logging_config import osint_logger


class TestRunner:
    """Main test runner for the OSINT framework"""
    
    def __init__(self):
        self.framework = test_framework
        self.results_dir = Path("tests/results")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
    async def run_all_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run all test suites"""
        
        print("🧠 OSINT Framework - Running All Tests")
        print("=" * 50)
        
        start_time = datetime.now()
        
        try:
            results = await self.framework.run_all_tests()
            
            if verbose:
                self._print_detailed_results(results)
            else:
                self._print_summary_results(results)
            
            # Generate reports
            self._generate_reports(results)
            
            return results
            
        except Exception as e:
            osint_logger.log_error(e, "test_runner")
            print(f"❌ Test execution failed: {e}")
            return {"error": str(e)}
    
    async def run_suite(self, suite_name: str, verbose: bool = False) -> Dict[str, Any]:
        """Run a specific test suite"""
        
        print(f"🧠 OSINT Framework - Running {suite_name} Tests")
        print("=" * 50)
        
        try:
            results = await self.framework.run_suite(suite_name)
            
            if verbose:
                self._print_suite_results(results)
            else:
                self._print_suite_summary(results)
            
            return results
            
        except Exception as e:
            osint_logger.log_error(e, "test_runner", {"suite": suite_name})
            print(f"❌ Test suite execution failed: {e}")
            return {"error": str(e)}
    
    def list_suites(self):
        """List available test suites"""
        
        print("🧠 Available Test Suites:")
        print("-" * 30)
        
        for suite_name, suite in self.framework.test_suites.items():
            test_count = len(suite.tests)
            print(f"  📋 {suite_name}: {test_count} tests")
        
        print()
    
    def _print_detailed_results(self, results: Dict[str, Any]):
        """Print detailed test results"""
        
        print(f"\n📊 Test Results Summary")
        print("-" * 30)
        print(f"Total Tests: {results['total_tests']}")
        print(f"Passed: {results['passed_tests']} ✅")
        print(f"Failed: {results['failed_tests']} ❌")
        print(f"Errors: {results['error_tests']} 🔥")
        print(f"Success Rate: {results['success_rate']:.1%}")
        print(f"Duration: {results['total_duration']:.2f}s")
        
        # Suite details
        print(f"\n📋 Suite Results:")
        for suite_name, suite_results in results['suite_results'].items():
            print(f"\n  {suite_name}:")
            print(f"    Tests: {suite_results['total_tests']}")
            print(f"    Passed: {suite_results['passed_tests']} ✅")
            print(f"    Failed: {suite_results['failed_tests']} ❌")
            print(f"    Errors: {suite_results['error_tests']} 🔥")
            print(f"    Success Rate: {suite_results['success_rate']:.1%}")
            print(f"    Duration: {suite_results['total_duration']:.2f}s")
        
        # Failed test details
        if results['failed_test_details']:
            print(f"\n❌ Failed Tests:")
            for failed_test in results['failed_test_details']:
                print(f"  - {failed_test['test_name']}: {failed_test['error']}")
    
    def _print_summary_results(self, results: Dict[str, Any]):
        """Print summary test results"""
        
        status_icon = "✅" if results['failed_tests'] == 0 and results['error_tests'] == 0 else "❌"
        
        print(f"\n{status_icon} Test Summary:")
        print(f"  {results['passed_tests']}/{results['total_tests']} tests passed")
        print(f"  Success rate: {results['success_rate']:.1%}")
        print(f"  Duration: {results['total_duration']:.2f}s")
        
        if results['failed_tests'] > 0 or results['error_tests'] > 0:
            print(f"\n❌ Issues found:")
            print(f"  Failed: {results['failed_tests']}")
            print(f"  Errors: {results['error_tests']}")
    
    def _print_suite_results(self, results: Dict[str, Any]):
        """Print detailed suite results"""
        
        suite_name = results['suite_name']
        summary = results['summary']
        
        print(f"\n📊 {suite_name} Results:")
        print("-" * 30)
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed_tests']} ✅")
        print(f"Failed: {summary['failed_tests']} ❌")
        print(f"Errors: {summary['error_tests']} 🔥")
        print(f"Success Rate: {summary['success_rate']:.1%}")
        print(f"Duration: {summary['total_duration']:.2f}s")
        
        # Individual test results
        print(f"\n📝 Individual Tests:")
        for test_result in results['results']:
            status_icon = {
                "passed": "✅",
                "failed": "❌",
                "error": "🔥",
                "skipped": "⏭️"
            }.get(test_result.status, "❓")
            
            print(f"  {status_icon} {test_result.test_name} ({test_result.duration:.2f}s)")
            
            if test_result.error:
                print(f"    Error: {test_result.error}")
    
    def _print_suite_summary(self, results: Dict[str, Any]):
        """Print summary suite results"""
        
        suite_name = results['suite_name']
        summary = results['summary']
        
        status_icon = "✅" if summary['failed_tests'] == 0 and summary['error_tests'] == 0 else "❌"
        
        print(f"\n{status_icon} {suite_name}:")
        print(f"  {summary['passed_tests']}/{summary['total_tests']} tests passed")
        print(f"  Success rate: {summary['success_rate']:.1%}")
        print(f"  Duration: {summary['total_duration']:.2f}s")
    
    def _generate_reports(self, results: Dict[str, Any]):
        """Generate test reports in various formats"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # JSON report
        json_report_path = self.results_dir / f"test_report_{timestamp}.json"
        with open(json_report_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # HTML report
        html_report_path = self.results_dir / f"test_report_{timestamp}.html"
        self._generate_html_report(results, html_report_path)
        
        # JUnit XML report (for CI/CD)
        junit_report_path = self.results_dir / f"junit_report_{timestamp}.xml"
        self._generate_junit_report(results, junit_report_path)
        
        print(f"\n📄 Reports generated:")
        print(f"  JSON: {json_report_path}")
        print(f"  HTML: {html_report_path}")
        print(f"  JUnit: {junit_report_path}")
    
    def _generate_html_report(self, results: Dict[str, Any], output_path: Path):
        """Generate HTML test report"""
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>OSINT Framework Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .summary {{ margin: 20px 0; }}
                .suite {{ margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; }}
                .suite-header {{ background-color: #f8f8f8; padding: 10px; font-weight: bold; }}
                .test {{ padding: 10px; border-bottom: 1px solid #eee; }}
                .passed {{ color: green; }}
                .failed {{ color: red; }}
                .error {{ color: orange; }}
                .skipped {{ color: gray; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🧠 OSINT Framework Test Report</h1>
                <p>Generated: {results['timestamp']}</p>
            </div>
            
            <div class="summary">
                <h2>Summary</h2>
                <p>Total Tests: {results['total_tests']}</p>
                <p>Passed: <span class="passed">{results['passed_tests']}</span></p>
                <p>Failed: <span class="failed">{results['failed_tests']}</span></p>
                <p>Errors: <span class="error">{results['error_tests']}</span></p>
                <p>Success Rate: {results['success_rate']:.1%}</p>
                <p>Duration: {results['total_duration']:.2f}s</p>
            </div>
            
            <div class="suites">
                <h2>Test Suites</h2>
        """
        
        for suite_name, suite_results in results['suite_results'].items():
            html_content += f"""
                <div class="suite">
                    <div class="suite-header">{suite_name}</div>
                    <p>Tests: {suite_results['total_tests']}, 
                       Passed: {suite_results['passed_tests']}, 
                       Failed: {suite_results['failed_tests']}, 
                       Success Rate: {suite_results['success_rate']:.1%}</p>
                </div>
            """
        
        html_content += """
            </div>
        </body>
        </html>
        """
        
        with open(output_path, 'w') as f:
            f.write(html_content)
    
    def _generate_junit_report(self, results: Dict[str, Any], output_path: Path):
        """Generate JUnit XML report for CI/CD integration"""
        
        xml_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="OSINT Framework Tests" 
            tests="{results['total_tests']}" 
            failures="{results['failed_tests']}" 
            errors="{results['error_tests']}" 
            time="{results['total_duration']:.2f}">
"""
        
        for suite_name, suite_results in results['suite_results'].items():
            xml_content += f"""
    <testsuite name="{suite_name}" 
               tests="{suite_results['total_tests']}" 
               failures="{suite_results['failed_tests']}" 
               errors="{suite_results['error_tests']}" 
               time="{suite_results['total_duration']:.2f}">
"""
            
            # Note: Individual test details would need to be tracked differently
            # for full JUnit compatibility
            
            xml_content += """
    </testsuite>
"""
        
        xml_content += """
</testsuites>
"""
        
        with open(output_path, 'w') as f:
            f.write(xml_content)


async def main():
    """Main test runner function"""
    
    parser = argparse.ArgumentParser(description="OSINT Framework Test Runner")
    parser.add_argument("--suite", "-s", help="Run specific test suite")
    parser.add_argument("--list", "-l", action="store_true", help="List available test suites")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--quick", "-q", action="store_true", help="Quick tests only (skip slow tests)")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.list:
        runner.list_suites()
        return
    
    if args.suite:
        results = await runner.run_suite(args.suite, verbose=args.verbose)
    else:
        results = await runner.run_all_tests(verbose=args.verbose)
    
    # Exit with error code if tests failed
    if "error" in results:
        sys.exit(1)
    elif results.get('failed_tests', 0) > 0 or results.get('error_tests', 0) > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())
