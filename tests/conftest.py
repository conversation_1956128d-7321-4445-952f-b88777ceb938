"""
🧠 CrewAI OSINT Agent Framework - Test Configuration

Comprehensive pytest configuration and fixtures for testing the OSINT framework.
Provides shared fixtures, test utilities, and configuration for unit, integration,
and end-to-end testing.

Features:
    - Mock API responses and external services
    - Test data generation and management
    - Database and file system isolation
    - Performance testing utilities
    - Security testing helpers
    - Async testing support
"""

import asyncio
import os
import tempfile
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, Generator, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient

# Test configuration
pytest_plugins = ["pytest_asyncio"]


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture
def mock_env_vars():
    """Mock environment variables for testing."""
    test_env = {
        "OPENAI_API_KEY": "sk-test-************************************",
        "SERPER_API_KEY": "test-serper-key-12345678901234567890",
        "OSINT_MASTER_KEY": "test-master-key-for-encryption-testing",
        "OSINT_LOG_LEVEL": "DEBUG",
        "OSINT_TEST_MODE": "true"
    }
    
    with patch.dict(os.environ, test_env, clear=False):
        yield test_env


@pytest.fixture
def sample_geopolitical_data():
    """Sample geopolitical analysis data for testing."""
    return {
        "region": "Middle East",
        "topic": "Regional tensions and diplomatic developments",
        "time_range": "7d",
        "analysis_type": "intelligence_brief",
        "include_economic": True,
        "include_security": True,
        "specific_topics": ["sanctions", "diplomatic relations", "military activities"]
    }


@pytest.fixture
def sample_cti_data():
    """Sample CTI analysis data for testing."""
    return {
        "threat_data": """
        Threat Report: APT28 Campaign Analysis
        
        IOCs identified:
        - IP: *************
        - Domain: malicious-domain.com
        - Hash: d41d8cd98f00b204e9800998ecf8427e
        - Email: <EMAIL>
        
        The campaign targets government entities using spear-phishing emails.
        """,
        "ioc_types": ["ip", "domain", "hash", "email"],
        "threat_actor": "APT28",
        "campaign_name": "Test Campaign 2024"
    }


@pytest.fixture
def sample_workflow_definition():
    """Sample workflow definition for testing."""
    return {
        "id": "test-workflow-001",
        "name": "Test Geopolitical Analysis Workflow",
        "description": "Test workflow for geopolitical analysis",
        "version": "1.0.0",
        "tasks": [
            {
                "id": "task-001",
                "name": "Data Collection",
                "task_type": "agent_task",
                "agent_type": "geopolitical",
                "parameters": {"region": "test-region"},
                "dependencies": []
            },
            {
                "id": "task-002", 
                "name": "Analysis",
                "task_type": "agent_task",
                "agent_type": "geopolitical",
                "parameters": {"analysis_type": "brief"},
                "dependencies": ["task-001"]
            }
        ]
    }


@pytest.fixture
def mock_openai_response():
    """Mock OpenAI API response for testing."""
    return {
        "choices": [
            {
                "message": {
                    "content": "This is a test response from the mocked OpenAI API."
                }
            }
        ],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 15,
            "total_tokens": 25
        }
    }


@pytest.fixture
def mock_serper_response():
    """Mock Serper API response for testing."""
    return {
        "searchParameters": {
            "q": "test query",
            "type": "search"
        },
        "organic": [
            {
                "title": "Test Search Result",
                "link": "https://example.com/test",
                "snippet": "This is a test search result snippet.",
                "date": "2024-01-01"
            }
        ]
    }


@pytest.fixture
def mock_crawl4ai_response():
    """Mock crawl4ai response for testing."""
    return {
        "success": True,
        "url": "https://example.com",
        "markdown": "# Test Page\n\nThis is test content from crawl4ai.",
        "cleaned_html": "<h1>Test Page</h1><p>This is test content from crawl4ai.</p>",
        "media": [],
        "links": ["https://example.com/link1", "https://example.com/link2"],
        "metadata": {
            "title": "Test Page",
            "description": "Test page description"
        }
    }


@pytest.fixture
def test_database_url(temp_dir):
    """Test database URL using SQLite in temp directory."""
    db_path = temp_dir / "test_osint.db"
    return f"sqlite:///{db_path}"


@pytest.fixture
def api_client(mock_env_vars):
    """FastAPI test client for API testing."""
    # Import here to avoid circular imports
    from api.main import app
    
    with TestClient(app) as client:
        yield client


class MockAgent:
    """Mock agent for testing workflows and integrations."""
    
    def __init__(self, agent_type: str = "test"):
        self.agent_type = agent_type
        self.call_count = 0
        self.last_input = None
    
    async def execute(self, input_data: Any) -> Dict[str, Any]:
        """Mock agent execution."""
        self.call_count += 1
        self.last_input = input_data
        
        return {
            "status": "success",
            "result": f"Mock {self.agent_type} agent result",
            "input_data": input_data,
            "execution_time": 0.1,
            "call_count": self.call_count
        }


@pytest.fixture
def mock_geo_agent():
    """Mock geopolitical agent for testing."""
    return MockAgent("geopolitical")


@pytest.fixture
def mock_cti_agent():
    """Mock CTI agent for testing."""
    return MockAgent("cti")


@pytest.fixture
def performance_timer():
    """Performance timing utility for tests."""
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = datetime.now()
        
        def stop(self):
            self.end_time = datetime.now()
            return self.duration
        
        @property
        def duration(self):
            if self.start_time and self.end_time:
                return (self.end_time - self.start_time).total_seconds()
            return None
    
    return Timer()


@pytest.fixture
def security_test_data():
    """Security testing data and utilities."""
    return {
        "malicious_inputs": [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "\x00\x01\x02\x03",
            "A" * 10000,  # Large input
        ],
        "valid_inputs": [
            "Normal text input",
            "Text with numbers 123",
            "Text-with-dashes_and_underscores",
        ],
        "test_urls": {
            "valid": ["https://example.com", "http://test.org"],
            "invalid": ["javascript:alert(1)", "file:///etc/passwd", "ftp://internal.local"],
            "malicious": ["http://127.0.0.1:8080", "https://localhost/admin"]
        }
    }


# Test utilities
def generate_test_id() -> str:
    """Generate a unique test ID."""
    return f"test-{uuid.uuid4().hex[:8]}"


def create_test_file(temp_dir: Path, filename: str, content: str) -> Path:
    """Create a test file with specified content."""
    file_path = temp_dir / filename
    file_path.write_text(content)
    return file_path


def assert_valid_response_format(response: Dict[str, Any]) -> None:
    """Assert that a response has the expected format."""
    assert "status" in response
    assert "timestamp" in response
    assert response["status"] in ["success", "error", "pending"]


def assert_performance_within_limits(duration: float, max_seconds: float) -> None:
    """Assert that operation completed within performance limits."""
    assert duration <= max_seconds, f"Operation took {duration}s, expected <= {max_seconds}s"


# Async test utilities
async def wait_for_condition(condition_func, timeout: float = 5.0, interval: float = 0.1):
    """Wait for a condition to become true with timeout."""
    start_time = datetime.now()
    
    while (datetime.now() - start_time).total_seconds() < timeout:
        if await condition_func() if asyncio.iscoroutinefunction(condition_func) else condition_func():
            return True
        await asyncio.sleep(interval)
    
    return False
