"""
🧠 CrewAI OSINT Agent Framework - API Integration Tests

Comprehensive integration tests for the FastAPI endpoints including
authentication, validation, error handling, and end-to-end workflows.

Test Coverage:
    - Authentication and authorization
    - Input validation and sanitization
    - Geopolitical analysis endpoints
    - CTI analysis endpoints
    - Workflow management endpoints
    - Security and encryption endpoints
    - Error handling and edge cases
"""

import json
import time
from unittest.mock import patch, AsyncMock

import pytest
from fastapi.testclient import TestClient


class TestAuthenticationEndpoints:
    """Test authentication and authorization endpoints."""
    
    def test_login_with_valid_credentials(self, api_client, mock_env_vars):
        """Test login with valid credentials."""
        response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_login_with_invalid_credentials(self, api_client, mock_env_vars):
        """Test login with invalid credentials."""
        response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "invalid", "password": "wrong"}
        )
        
        assert response.status_code == 401
        assert "Invalid credentials" in response.json()["detail"]
    
    def test_protected_endpoint_without_token(self, api_client):
        """Test accessing protected endpoint without token."""
        response = api_client.get("/api/v1/analysis/tasks")
        
        assert response.status_code == 401
    
    def test_protected_endpoint_with_valid_token(self, api_client, mock_env_vars):
        """Test accessing protected endpoint with valid token."""
        # First login to get token
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Use token to access protected endpoint
        response = api_client.get(
            "/api/v1/analysis/tasks",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200


class TestGeopoliticalAnalysisEndpoints:
    """Test geopolitical analysis endpoints."""
    
    def test_start_geopolitical_analysis(self, api_client, mock_env_vars, sample_geopolitical_data):
        """Test starting geopolitical analysis."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Start analysis
        response = api_client.post(
            "/api/v1/geopolitical/analyze",
            json=sample_geopolitical_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data
        assert data["status"] == "pending"
    
    def test_geopolitical_analysis_input_validation(self, api_client, mock_env_vars):
        """Test input validation for geopolitical analysis."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Test with invalid time_range
        invalid_data = {
            "topic": "Test topic",
            "regions": ["Middle East"],
            "time_range": "invalid_range"
        }
        
        response = api_client.post(
            "/api/v1/geopolitical/analyze",
            json=invalid_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 400
        assert "Invalid time_range" in response.json()["detail"]
    
    def test_geopolitical_analysis_too_many_regions(self, api_client, mock_env_vars):
        """Test validation for too many regions."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Test with too many regions
        invalid_data = {
            "topic": "Test topic",
            "regions": [f"Region{i}" for i in range(15)],  # More than 10
            "time_range": "7d"
        }
        
        response = api_client.post(
            "/api/v1/geopolitical/analyze",
            json=invalid_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 400
        assert "Too many regions" in response.json()["detail"]
    
    def test_situation_report_generation(self, api_client, mock_env_vars):
        """Test situation report generation."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Generate situation report
        response = api_client.post(
            "/api/v1/geopolitical/situation-report",
            json={"regions": ["Asia", "Europe"], "time_range": "24h"},
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data


class TestCTIAnalysisEndpoints:
    """Test CTI analysis endpoints."""
    
    def test_ioc_extraction(self, api_client, mock_env_vars, sample_cti_data):
        """Test IOC extraction endpoint."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Extract IOCs
        response = api_client.post(
            "/api/v1/cti/extract-iocs",
            json={"threat_data": sample_cti_data["threat_data"]},
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data
    
    def test_threat_actor_tracking(self, api_client, mock_env_vars):
        """Test threat actor tracking endpoint."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Track threat actor
        response = api_client.post(
            "/api/v1/cti/track-actor",
            json={"actor_name": "APT28", "search_type": "recent_activity"},
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data


class TestTaskManagementEndpoints:
    """Test task management endpoints."""
    
    def test_list_tasks(self, api_client, mock_env_vars):
        """Test listing tasks."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # List tasks
        response = api_client.get(
            "/api/v1/analysis/tasks",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "tasks" in data
        assert isinstance(data["tasks"], list)
    
    def test_get_task_status(self, api_client, mock_env_vars, sample_geopolitical_data):
        """Test getting task status."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Start a task
        task_response = api_client.post(
            "/api/v1/geopolitical/analyze",
            json=sample_geopolitical_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        task_id = task_response.json()["task_id"]
        
        # Get task status
        response = api_client.get(
            f"/api/v1/analysis/tasks/{task_id}",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == task_id
        assert "status" in data


class TestSecurityEndpoints:
    """Test security and encryption endpoints."""
    
    def test_encrypt_data(self, api_client, mock_env_vars):
        """Test data encryption endpoint."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Encrypt data
        test_data = "This is sensitive test data"
        response = api_client.post(
            "/api/v1/security/encrypt",
            json={"data": test_data},
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "encrypted_data" in data
        assert data["encrypted_data"] != test_data
    
    def test_decrypt_data(self, api_client, mock_env_vars):
        """Test data decryption endpoint."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # First encrypt some data
        test_data = "This is sensitive test data"
        encrypt_response = api_client.post(
            "/api/v1/security/encrypt",
            json={"data": test_data},
            headers={"Authorization": f"Bearer {token}"}
        )
        encrypted_data = encrypt_response.json()["encrypted_data"]
        
        # Then decrypt it
        response = api_client.post(
            "/api/v1/security/decrypt",
            json={"encrypted_data": encrypted_data},
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["decrypted_data"] == test_data
    
    def test_scan_sensitive_data(self, api_client, mock_env_vars):
        """Test sensitive data scanning endpoint."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Scan text with sensitive data
        test_text = "Contact <NAME_EMAIL> or 555-123-4567"
        response = api_client.post(
            "/api/v1/security/scan-sensitive",
            json={"text": test_text},
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "sensitive_data_found" in data
        assert len(data["sensitive_data_found"]) > 0


class TestErrorHandling:
    """Test error handling across endpoints."""
    
    def test_missing_api_keys(self, api_client):
        """Test behavior when API keys are missing."""
        with patch.dict('os.environ', {}, clear=True):
            response = api_client.post(
                "/api/v1/geopolitical/analyze",
                json={"topic": "test", "regions": ["test"]}
            )
            
            assert response.status_code == 400
            assert "API keys not configured" in response.json()["detail"]
    
    def test_malformed_json(self, api_client, mock_env_vars):
        """Test handling of malformed JSON."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Send malformed JSON
        response = api_client.post(
            "/api/v1/geopolitical/analyze",
            data="invalid json",
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
        )
        
        assert response.status_code == 422
    
    def test_large_request_handling(self, api_client, mock_env_vars):
        """Test handling of very large requests."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Send very large request
        large_data = {
            "topic": "A" * 50000,  # Very large topic
            "regions": ["test"]
        }
        
        response = api_client.post(
            "/api/v1/geopolitical/analyze",
            json=large_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 400
        assert "too long" in response.json()["detail"].lower()


class TestPerformanceAndReliability:
    """Test performance and reliability aspects."""
    
    def test_concurrent_requests(self, api_client, mock_env_vars):
        """Test handling of concurrent requests."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Make multiple concurrent requests
        import threading
        results = []
        
        def make_request():
            response = api_client.get(
                "/api/v1/analysis/tasks",
                headers={"Authorization": f"Bearer {token}"}
            )
            results.append(response.status_code)
        
        threads = [threading.Thread(target=make_request) for _ in range(5)]
        for thread in threads:
            thread.start()
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert all(status == 200 for status in results)
    
    def test_response_time_requirements(self, api_client, mock_env_vars, performance_timer):
        """Test that API responses meet time requirements."""
        # Login first
        login_response = api_client.post(
            "/api/v1/auth/login",
            json={"username": "admin", "password": "admin123"}
        )
        token = login_response.json()["access_token"]
        
        # Test response time for simple endpoint
        performance_timer.start()
        response = api_client.get(
            "/api/v1/analysis/tasks",
            headers={"Authorization": f"Bearer {token}"}
        )
        duration = performance_timer.stop()
        
        assert response.status_code == 200
        assert duration < 2.0, f"Response took {duration}s, expected < 2.0s"
