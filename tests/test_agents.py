"""
🧠 CrewAI OSINT Agent Framework - Agent Tests

Comprehensive tests for OSINT agents including functionality,
performance, and integration testing.
"""

import pytest
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tests.test_framework import (
    TestSuite, TestResult, test_framework, TestDataGenerator,
    assert_response_valid, assert_analysis_result_valid, 
    assert_no_sensitive_data_leaked, measure_performance
)
from agents.geo_agent import GeopoliticalAgent
from agents.cti_agent import CTIAgent
from agents.stateful_geo_agent import StatefulGeopoliticalAgent
from agents.stateful_cti_agent import StatefulCTIAgent
from utils.logging_config import osint_logger


class AgentTestSuite(TestSuite):
    """Test suite for OSINT agents"""
    
    def __init__(self):
        super().__init__("Agent Tests")
        self.test_data = TestDataGenerator()
        self.setup_tests()
    
    def setup_tests(self):
        """Setup all agent tests"""
        
        # Basic agent tests
        self.add_test(self.test_geo_agent_initialization, "Geopolitical Agent Initialization")
        self.add_test(self.test_cti_agent_initialization, "CTI Agent Initialization")
        
        # Functionality tests
        self.add_test(self.test_geo_agent_analysis, "Geopolitical Agent Analysis", timeout=120)
        self.add_test(self.test_cti_agent_analysis, "CTI Agent Analysis", timeout=120)
        
        # Stateful agent tests
        self.add_test(self.test_stateful_geo_agent, "Stateful Geopolitical Agent", timeout=120)
        self.add_test(self.test_stateful_cti_agent, "Stateful CTI Agent", timeout=120)
        
        # Performance tests
        self.add_test(self.test_agent_performance, "Agent Performance", timeout=60)
        
        # Error handling tests
        self.add_test(self.test_agent_error_handling, "Agent Error Handling")
        
        # Security tests
        self.add_test(self.test_agent_security, "Agent Security")
    
    def test_geo_agent_initialization(self):
        """Test geopolitical agent initialization"""
        
        agent = GeopoliticalAgent()
        
        # Check agent attributes
        assert hasattr(agent, 'serper'), "Agent should have serper tool"
        assert hasattr(agent, 'crawler'), "Agent should have crawler tool"
        
        # Check agent methods
        assert callable(getattr(agent, 'generate_intelligence_brief', None)), "Agent should have generate_intelligence_brief method"
        assert callable(getattr(agent, 'setup_regional_monitoring', None)), "Agent should have setup_regional_monitoring method"
        
        osint_logger.logger.info("Geopolitical agent initialization test passed")
    
    def test_cti_agent_initialization(self):
        """Test CTI agent initialization"""
        
        agent = CTIAgent()
        
        # Check agent attributes
        assert hasattr(agent, 'serper'), "Agent should have serper tool"
        assert hasattr(agent, 'crawler'), "Agent should have crawler tool"
        
        # Check agent methods
        assert callable(getattr(agent, 'extract_iocs', None)), "Agent should have extract_iocs method"
        assert callable(getattr(agent, 'correlate_campaign_data', None)), "Agent should have correlate_campaign_data method"
        
        osint_logger.logger.info("CTI agent initialization test passed")
    
    def test_geo_agent_analysis(self):
        """Test geopolitical agent analysis functionality"""
        
        agent = GeopoliticalAgent()
        
        # Test intelligence brief generation
        try:
            result = agent.generate_intelligence_brief(
                topic="Test geopolitical analysis",
                regions=["Global"],
                time_range="7d"
            )
            
            # Validate result structure
            assert_analysis_result_valid(result)
            assert "analysis" in result, "Result should contain analysis"
            assert "sources" in result, "Result should contain sources"
            
            # Check for sensitive data
            assert_no_sensitive_data_leaked(result)
            
            osint_logger.logger.info("Geopolitical agent analysis test passed")
            
        except Exception as e:
            # Log the error but don't fail the test if it's due to API limits
            if "rate limit" in str(e).lower() or "quota" in str(e).lower():
                osint_logger.logger.warning(f"API limit reached in test: {e}")
                pytest.skip("API limit reached")
            else:
                raise
    
    def test_cti_agent_analysis(self):
        """Test CTI agent analysis functionality"""
        
        agent = CTIAgent()
        
        # Test IOC extraction
        sample_text = self.test_data.generate_sample_text()
        
        try:
            result = agent.extract_iocs(sample_text)
            
            # Validate result structure
            assert isinstance(result, list), "IOC extraction should return a list"
            
            # Each IOC should have required fields
            for ioc in result:
                assert "type" in ioc, "IOC should have type field"
                assert "value" in ioc, "IOC should have value field"
            
            # Check for sensitive data
            assert_no_sensitive_data_leaked(result)
            
            osint_logger.logger.info("CTI agent analysis test passed")
            
        except Exception as e:
            if "rate limit" in str(e).lower() or "quota" in str(e).lower():
                osint_logger.logger.warning(f"API limit reached in test: {e}")
                pytest.skip("API limit reached")
            else:
                raise
    
    def test_stateful_geo_agent(self):
        """Test stateful geopolitical agent"""
        
        agent = StatefulGeopoliticalAgent(user_id="test_user")
        
        # Test conversation creation
        conversation_id = agent.start_conversation({
            "test_context": "automated_testing"
        })
        
        assert conversation_id, "Conversation ID should be created"
        assert agent.conversation_id == conversation_id, "Agent should track conversation ID"
        
        # Test contextual analysis
        try:
            result = agent.analyze_with_context(
                query="Test query for stateful analysis",
                analysis_type="intelligence_brief"
            )
            
            # Validate result
            assert_analysis_result_valid(result)
            assert "conversation_context" in result, "Result should contain conversation context"
            
            # Check conversation context
            context = result["conversation_context"]
            assert context["conversation_id"] == conversation_id, "Context should match conversation ID"
            
            osint_logger.logger.info("Stateful geopolitical agent test passed")
            
        except Exception as e:
            if "rate limit" in str(e).lower() or "quota" in str(e).lower():
                osint_logger.logger.warning(f"API limit reached in test: {e}")
                pytest.skip("API limit reached")
            else:
                raise
    
    def test_stateful_cti_agent(self):
        """Test stateful CTI agent"""
        
        agent = StatefulCTIAgent(user_id="test_user")
        
        # Test conversation creation
        conversation_id = agent.start_conversation({
            "test_context": "automated_testing"
        })
        
        assert conversation_id, "Conversation ID should be created"
        
        # Test contextual analysis
        sample_text = self.test_data.generate_sample_text()
        
        try:
            result = agent.analyze_threat_with_context(
                content=sample_text,
                analysis_type="ioc_extraction"
            )
            
            # Validate result
            assert_analysis_result_valid(result)
            assert "conversation_context" in result, "Result should contain conversation context"
            
            osint_logger.logger.info("Stateful CTI agent test passed")
            
        except Exception as e:
            if "rate limit" in str(e).lower() or "quota" in str(e).lower():
                osint_logger.logger.warning(f"API limit reached in test: {e}")
                pytest.skip("API limit reached")
            else:
                raise
    
    def test_agent_performance(self):
        """Test agent performance"""
        
        def quick_geo_analysis():
            agent = GeopoliticalAgent()
            return agent.generate_intelligence_brief(
                topic="Quick test",
                regions=["Global"],
                time_range="1d"
            )
        
        # Measure performance
        perf_result = measure_performance(quick_geo_analysis, max_duration=30.0)
        
        assert perf_result["performance_ok"], f"Performance test failed: {perf_result.get('error', 'Unknown error')}"
        
        osint_logger.logger.info(f"Agent performance test passed - Duration: {perf_result['duration']:.2f}s")
    
    def test_agent_error_handling(self):
        """Test agent error handling"""
        
        agent = GeopoliticalAgent()
        
        # Test with invalid input
        try:
            result = agent.generate_intelligence_brief(
                topic="",  # Empty topic
                regions=[],  # Empty regions
                time_range="invalid"  # Invalid time range
            )
            
            # Should handle gracefully
            assert isinstance(result, dict), "Agent should return dict even with invalid input"
            
        except Exception as e:
            # Should not raise unhandled exceptions
            assert False, f"Agent should handle errors gracefully, but raised: {e}"
        
        osint_logger.logger.info("Agent error handling test passed")
    
    def test_agent_security(self):
        """Test agent security features"""
        
        agent = GeopoliticalAgent()
        
        # Test with potentially malicious input
        malicious_inputs = [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "javascript:alert('xss')"
        ]
        
        for malicious_input in malicious_inputs:
            try:
                result = agent.generate_intelligence_brief(
                    topic=malicious_input,
                    regions=["Global"],
                    time_range="1d"
                )
                
                # Check that malicious input is not reflected in output
                result_str = json.dumps(result)
                assert malicious_input not in result_str, f"Malicious input reflected in output: {malicious_input}"
                
            except Exception as e:
                # It's okay if the agent rejects malicious input
                osint_logger.logger.info(f"Agent rejected malicious input: {malicious_input}")
        
        osint_logger.logger.info("Agent security test passed")


# Multi-modal agent tests
class MultiModalAgentTestSuite(TestSuite):
    """Test suite for multi-modal OSINT agents"""
    
    def __init__(self):
        super().__init__("Multi-Modal Agent Tests")
        self.test_data = TestDataGenerator()
        self.setup_tests()
    
    def setup_tests(self):
        """Setup multi-modal agent tests"""
        
        self.add_test(self.test_multimodal_agent_initialization, "Multi-Modal Agent Initialization")
        self.add_test(self.test_image_analysis, "Image Analysis", timeout=120)
        self.add_test(self.test_batch_analysis, "Batch Analysis", timeout=180)
    
    def test_multimodal_agent_initialization(self):
        """Test multi-modal agent initialization"""
        
        try:
            from agents.multimodal_osint_agent import MultiModalOSINTAgent
            
            agent = MultiModalOSINTAgent(user_id="test_user")
            
            # Check agent attributes
            assert hasattr(agent, 'multimodal_analyzer'), "Agent should have multimodal analyzer"
            assert hasattr(agent, 'serper'), "Agent should have serper tool"
            
            osint_logger.logger.info("Multi-modal agent initialization test passed")
            
        except ImportError as e:
            pytest.skip(f"Multi-modal dependencies not available: {e}")
    
    def test_image_analysis(self):
        """Test image analysis functionality"""
        
        try:
            from agents.multimodal_osint_agent import MultiModalOSINTAgent
            
            agent = MultiModalOSINTAgent(user_id="test_user")
            
            # Create test image
            test_image_path = Path("tests/data/test_image.png")
            test_image_path.parent.mkdir(parents=True, exist_ok=True)
            
            self.test_data.create_sample_image(test_image_path)
            
            # Test image analysis
            result = agent.analyze_media_with_context(
                media_input=test_image_path,
                media_type="image",
                analysis_types=["metadata", "ocr"],
                osint_context=False  # Skip OSINT to avoid API calls
            )
            
            # Validate result
            assert_analysis_result_valid(result)
            assert "multimodal_analysis" in result, "Result should contain multimodal analysis"
            
            # Clean up
            if test_image_path.exists():
                test_image_path.unlink()
            
            osint_logger.logger.info("Image analysis test passed")
            
        except ImportError as e:
            pytest.skip(f"Multi-modal dependencies not available: {e}")
    
    def test_batch_analysis(self):
        """Test batch analysis functionality"""
        
        try:
            from agents.multimodal_osint_agent import MultiModalOSINTAgent
            
            agent = MultiModalOSINTAgent(user_id="test_user")
            
            # Create test images
            test_images = []
            for i in range(3):
                test_image_path = Path(f"tests/data/test_image_{i}.png")
                test_image_path.parent.mkdir(parents=True, exist_ok=True)
                self.test_data.create_sample_image(test_image_path)
                test_images.append(test_image_path)
            
            # Test batch analysis
            result = agent.batch_analyze_media(
                media_files=test_images,
                analysis_types=["metadata"]
            )
            
            # Validate result
            assert "batch_info" in result, "Result should contain batch info"
            assert "results" in result, "Result should contain results"
            assert "summary" in result, "Result should contain summary"
            
            # Check batch info
            assert result["batch_info"]["total_files"] == 3, "Should process 3 files"
            
            # Clean up
            for test_image in test_images:
                if test_image.exists():
                    test_image.unlink()
            
            osint_logger.logger.info("Batch analysis test passed")
            
        except ImportError as e:
            pytest.skip(f"Multi-modal dependencies not available: {e}")


# Browser automation agent tests
class BrowserAgentTestSuite(TestSuite):
    """Test suite for browser automation agents"""
    
    def __init__(self):
        super().__init__("Browser Agent Tests")
        self.test_data = TestDataGenerator()
        self.setup_tests()
    
    def setup_tests(self):
        """Setup browser agent tests"""
        
        self.add_test(self.test_browser_agent_initialization, "Browser Agent Initialization")
        self.add_test(self.test_web_investigation, "Web Investigation", timeout=180)
    
    def test_browser_agent_initialization(self):
        """Test browser agent initialization"""
        
        try:
            from agents.browser_osint_agent import BrowserOSINTAgent
            
            agent = BrowserOSINTAgent(user_id="test_user", headless=True)
            
            # Check agent attributes
            assert hasattr(agent, 'serper'), "Agent should have serper tool"
            assert agent.headless == True, "Agent should be in headless mode"
            
            osint_logger.logger.info("Browser agent initialization test passed")
            
        except ImportError as e:
            pytest.skip(f"Browser automation dependencies not available: {e}")
    
    def test_web_investigation(self):
        """Test web investigation functionality"""
        
        try:
            from agents.browser_osint_agent import BrowserOSINTAgent
            
            agent = BrowserOSINTAgent(user_id="test_user", headless=True)
            
            # Test investigation with a safe URL
            result = agent.investigate_target(
                target="example.com",
                investigation_type="web_analysis",
                use_stealth=False
            )
            
            # Validate result
            assert_analysis_result_valid(result)
            assert "search_results" in result, "Result should contain search results"
            assert "web_analysis" in result, "Result should contain web analysis"
            
            osint_logger.logger.info("Web investigation test passed")
            
        except ImportError as e:
            pytest.skip(f"Browser automation dependencies not available: {e}")
        except Exception as e:
            if "webdriver" in str(e).lower() or "chrome" in str(e).lower():
                pytest.skip(f"Browser driver not available: {e}")
            else:
                raise


# Register test suites
agent_test_suite = AgentTestSuite()
multimodal_test_suite = MultiModalAgentTestSuite()
browser_test_suite = BrowserAgentTestSuite()

test_framework.register_suite(agent_test_suite)
test_framework.register_suite(multimodal_test_suite)
test_framework.register_suite(browser_test_suite)
