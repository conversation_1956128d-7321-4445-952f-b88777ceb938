"""
🧠 CrewAI OSINT Agent Framework - Automated Testing Pipeline

Comprehensive testing framework for continuous evaluation and quality assurance
of OSINT agents, tools, and workflows.
"""

import pytest
import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
import sys
import tempfile
import shutil

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.logging_config import osint_logger
from utils.monitoring import metrics_collector, health_checker


class TestResult:
    """Test result container"""
    
    def __init__(self, test_name: str, status: str, duration: float, 
                 details: Dict[str, Any] = None, error: str = None):
        self.test_name = test_name
        self.status = status  # "passed", "failed", "skipped", "error"
        self.duration = duration
        self.details = details or {}
        self.error = error
        self.timestamp = datetime.now().isoformat()


class TestSuite:
    """Base test suite class"""
    
    def __init__(self, name: str):
        self.name = name
        self.tests = []
        self.setup_methods = []
        self.teardown_methods = []
        self.results = []
    
    def add_test(self, test_func: Callable, name: str = None, timeout: int = 60):
        """Add a test to the suite"""
        self.tests.append({
            "function": test_func,
            "name": name or test_func.__name__,
            "timeout": timeout
        })
    
    def add_setup(self, setup_func: Callable):
        """Add setup method"""
        self.setup_methods.append(setup_func)
    
    def add_teardown(self, teardown_func: Callable):
        """Add teardown method"""
        self.teardown_methods.append(teardown_func)
    
    async def run_tests(self) -> List[TestResult]:
        """Run all tests in the suite"""
        
        osint_logger.logger.info(f"Running test suite: {self.name}")
        
        # Run setup methods
        for setup_func in self.setup_methods:
            try:
                if asyncio.iscoroutinefunction(setup_func):
                    await setup_func()
                else:
                    setup_func()
            except Exception as e:
                osint_logger.log_error(e, "test_setup", {"suite": self.name})
        
        # Run tests
        for test_info in self.tests:
            result = await self._run_single_test(test_info)
            self.results.append(result)
        
        # Run teardown methods
        for teardown_func in self.teardown_methods:
            try:
                if asyncio.iscoroutinefunction(teardown_func):
                    await teardown_func()
                else:
                    teardown_func()
            except Exception as e:
                osint_logger.log_error(e, "test_teardown", {"suite": self.name})
        
        return self.results
    
    async def _run_single_test(self, test_info: Dict[str, Any]) -> TestResult:
        """Run a single test"""
        
        test_name = test_info["name"]
        test_func = test_info["function"]
        timeout = test_info["timeout"]
        
        start_time = time.time()
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                await asyncio.wait_for(test_func(), timeout=timeout)
            else:
                test_func()
            
            duration = time.time() - start_time
            
            return TestResult(
                test_name=test_name,
                status="passed",
                duration=duration
            )
            
        except asyncio.TimeoutError:
            duration = time.time() - start_time
            return TestResult(
                test_name=test_name,
                status="failed",
                duration=duration,
                error="Test timeout"
            )
            
        except AssertionError as e:
            duration = time.time() - start_time
            return TestResult(
                test_name=test_name,
                status="failed",
                duration=duration,
                error=str(e)
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                test_name=test_name,
                status="error",
                duration=duration,
                error=str(e)
            )


class OSINTTestFramework:
    """Main testing framework for OSINT components"""
    
    def __init__(self):
        self.test_suites = {}
        self.test_results = []
        self.test_data_dir = Path("tests/data")
        self.test_data_dir.mkdir(parents=True, exist_ok=True)
        
        osint_logger.logger.info("OSINT test framework initialized")
    
    def register_suite(self, suite: TestSuite):
        """Register a test suite"""
        self.test_suites[suite.name] = suite
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all registered test suites"""
        
        start_time = time.time()
        all_results = {}
        
        for suite_name, suite in self.test_suites.items():
            suite_results = await suite.run_tests()
            all_results[suite_name] = suite_results
            self.test_results.extend(suite_results)
        
        total_duration = time.time() - start_time
        
        # Generate summary
        summary = self._generate_test_summary(all_results, total_duration)
        
        # Save results
        self._save_test_results(summary)
        
        return summary
    
    async def run_suite(self, suite_name: str) -> Dict[str, Any]:
        """Run a specific test suite"""
        
        if suite_name not in self.test_suites:
            raise ValueError(f"Test suite '{suite_name}' not found")
        
        suite = self.test_suites[suite_name]
        results = await suite.run_tests()
        
        return {
            "suite_name": suite_name,
            "results": results,
            "summary": self._generate_suite_summary(results)
        }
    
    def _generate_test_summary(self, all_results: Dict[str, List[TestResult]], 
                              total_duration: float) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        
        total_tests = sum(len(results) for results in all_results.values())
        passed_tests = sum(
            len([r for r in results if r.status == "passed"]) 
            for results in all_results.values()
        )
        failed_tests = sum(
            len([r for r in results if r.status == "failed"]) 
            for results in all_results.values()
        )
        error_tests = sum(
            len([r for r in results if r.status == "error"]) 
            for results in all_results.values()
        )
        
        return {
            "timestamp": datetime.now().isoformat(),
            "total_duration": total_duration,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "suite_results": {
                suite_name: self._generate_suite_summary(results)
                for suite_name, results in all_results.items()
            },
            "failed_test_details": [
                {
                    "test_name": result.test_name,
                    "error": result.error,
                    "duration": result.duration
                }
                for results in all_results.values()
                for result in results
                if result.status in ["failed", "error"]
            ]
        }
    
    def _generate_suite_summary(self, results: List[TestResult]) -> Dict[str, Any]:
        """Generate summary for a test suite"""
        
        total_tests = len(results)
        passed_tests = len([r for r in results if r.status == "passed"])
        failed_tests = len([r for r in results if r.status == "failed"])
        error_tests = len([r for r in results if r.status == "error"])
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "total_duration": sum(r.duration for r in results),
            "average_duration": sum(r.duration for r in results) / total_tests if total_tests > 0 else 0
        }
    
    def _save_test_results(self, summary: Dict[str, Any]):
        """Save test results to file"""
        
        try:
            results_dir = Path("tests/results")
            results_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = results_dir / f"test_results_{timestamp}.json"
            
            with open(results_file, 'w') as f:
                json.dump(summary, f, indent=2)
            
            # Also save as latest
            latest_file = results_dir / "latest_results.json"
            with open(latest_file, 'w') as f:
                json.dump(summary, f, indent=2)
            
            osint_logger.logger.info(f"Test results saved to {results_file}")
            
        except Exception as e:
            osint_logger.log_error(e, "test_results_save")


# Test data generators
class TestDataGenerator:
    """Generate test data for OSINT testing"""
    
    @staticmethod
    def generate_sample_urls() -> List[str]:
        """Generate sample URLs for testing"""
        return [
            "https://httpbin.org/get",
            "https://httpbin.org/json",
            "https://httpbin.org/html",
            "https://example.com",
            "https://httpstat.us/200"
        ]
    
    @staticmethod
    def generate_sample_domains() -> List[str]:
        """Generate sample domains for testing"""
        return [
            "example.com",
            "httpbin.org",
            "google.com",
            "github.com",
            "stackoverflow.com"
        ]
    
    @staticmethod
    def generate_sample_search_queries() -> List[str]:
        """Generate sample search queries"""
        return [
            "OSINT techniques",
            "cybersecurity news",
            "threat intelligence",
            "data breach 2024",
            "malware analysis"
        ]
    
    @staticmethod
    def generate_sample_text() -> str:
        """Generate sample text for analysis"""
        return """
        This is a sample text for testing OSINT analysis capabilities.
        It contains various types of information including:
        - Email: <EMAIL>
        - Phone: ************
        - IP Address: ***********
        - URL: https://example.com/test
        
        This text can be used to test text extraction, entity recognition,
        and other natural language processing capabilities.
        """
    
    @staticmethod
    def create_sample_image(output_path: Path) -> Path:
        """Create a sample image for testing"""
        
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # Create a simple test image
            img = Image.new('RGB', (400, 200), color='white')
            draw = ImageDraw.Draw(img)
            
            # Add some text
            try:
                # Try to use a default font
                font = ImageFont.load_default()
            except:
                font = None
            
            draw.text((50, 50), "OSINT Test Image", fill='black', font=font)
            draw.text((50, 100), "Sample text for OCR testing", fill='blue', font=font)
            draw.rectangle([50, 150, 350, 180], outline='red', width=2)
            
            img.save(output_path)
            return output_path
            
        except ImportError:
            # If PIL not available, create a simple text file instead
            with open(output_path.with_suffix('.txt'), 'w') as f:
                f.write("Sample image placeholder - PIL not available")
            return output_path.with_suffix('.txt')
    
    @staticmethod
    def create_sample_document(output_path: Path, content: str = None) -> Path:
        """Create a sample document for testing"""
        
        if content is None:
            content = TestDataGenerator.generate_sample_text()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return output_path


# Global test framework instance
test_framework = OSINTTestFramework()


# Utility functions for tests
def assert_response_valid(response: Dict[str, Any], required_fields: List[str] = None):
    """Assert that a response is valid"""
    
    assert isinstance(response, dict), "Response must be a dictionary"
    
    if required_fields:
        for field in required_fields:
            assert field in response, f"Required field '{field}' missing from response"


def assert_analysis_result_valid(result: Dict[str, Any]):
    """Assert that an analysis result is valid"""
    
    required_fields = ["timestamp", "status"]
    assert_response_valid(result, required_fields)
    
    assert result["status"] in ["completed", "failed", "pending"], "Invalid status"


def assert_no_sensitive_data_leaked(data: Any):
    """Assert that no sensitive data is leaked in response"""
    
    if isinstance(data, dict):
        data_str = json.dumps(data)
    else:
        data_str = str(data)
    
    # Check for common sensitive patterns
    sensitive_patterns = [
        "password", "api_key", "secret", "token", "private_key"
    ]
    
    data_lower = data_str.lower()
    for pattern in sensitive_patterns:
        assert pattern not in data_lower, f"Potential sensitive data leak: {pattern}"


def measure_performance(func: Callable, max_duration: float = 10.0) -> Dict[str, Any]:
    """Measure function performance"""
    
    start_time = time.time()
    
    try:
        if asyncio.iscoroutinefunction(func):
            result = asyncio.run(func())
        else:
            result = func()
        
        duration = time.time() - start_time
        
        assert duration <= max_duration, f"Function took too long: {duration:.2f}s > {max_duration}s"
        
        return {
            "result": result,
            "duration": duration,
            "performance_ok": True
        }
        
    except Exception as e:
        duration = time.time() - start_time
        return {
            "error": str(e),
            "duration": duration,
            "performance_ok": False
        }
