"""
🧠 CrewAI OSINT Agent Framework - Tools Tests

Comprehensive tests for OSINT tools including search engines,
crawlers, analyzers, and utility functions.
"""

import pytest
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path
import sys
import tempfile

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tests.test_framework import (
    TestSuite, TestResult, test_framework, TestDataGenerator,
    assert_response_valid, assert_analysis_result_valid, 
    assert_no_sensitive_data_leaked, measure_performance
)
from tools.serper_wrapper import SerperSearchTool
from tools.crawl4ai_wrapper import Crawl4AITool
from utils.logging_config import osint_logger


class ToolsTestSuite(TestSuite):
    """Test suite for OSINT tools"""
    
    def __init__(self):
        super().__init__("Tools Tests")
        self.test_data = TestDataGenerator()
        self.setup_tests()
    
    def setup_tests(self):
        """Setup all tool tests"""
        
        # Search engine tests
        self.add_test(self.test_serper_initialization, "Serper Initialization")
        self.add_test(self.test_serper_search, "Serper Search", timeout=30)
        self.add_test(self.test_serper_news_search, "Serper News Search", timeout=30)
        self.add_test(self.test_serper_image_search, "Serper Image Search", timeout=30)
        
        # Crawler tests
        self.add_test(self.test_crawler_initialization, "Crawler Initialization")
        self.add_test(self.test_crawler_basic, "Basic Crawling", timeout=60)
        self.add_test(self.test_crawler_batch, "Batch Crawling", timeout=120)
        
        # Performance tests
        self.add_test(self.test_tools_performance, "Tools Performance")
        
        # Error handling tests
        self.add_test(self.test_tools_error_handling, "Tools Error Handling")
    
    def test_serper_initialization(self):
        """Test Serper wrapper initialization"""
        
        serper = SerperSearchTool()
        
        # Check initialization
        assert hasattr(serper, 'api_key'), "Serper should have api_key attribute"
        assert hasattr(serper, 'base_url'), "Serper should have base_url attribute"
        
        # Check methods
        assert callable(getattr(serper, 'search', None)), "Serper should have search method"
        
        osint_logger.logger.info("Serper initialization test passed")
    
    def test_serper_search(self):
        """Test Serper search functionality"""
        
        serper = SerperSearchTool()
        
        try:
            # Test basic search
            result = serper.search("OSINT testing", search_type="search")
            
            # Validate result structure
            assert isinstance(result, dict), "Search result should be a dictionary"
            
            # Check for expected fields
            expected_fields = ["organic"]
            for field in expected_fields:
                if field in result:
                    assert isinstance(result[field], list), f"{field} should be a list"
            
            # Check for sensitive data
            assert_no_sensitive_data_leaked(result)
            
            osint_logger.logger.info("Serper search test passed")
            
        except Exception as e:
            if "api key" in str(e).lower() or "unauthorized" in str(e).lower():
                pytest.skip("Serper API key not configured")
            elif "rate limit" in str(e).lower() or "quota" in str(e).lower():
                pytest.skip("API rate limit reached")
            else:
                raise
    
    def test_serper_news_search(self):
        """Test Serper news search"""
        
        serper = SerperSearchTool()
        
        try:
            result = serper.search("cybersecurity", search_type="news")
            
            # Validate result
            assert isinstance(result, dict), "News search result should be a dictionary"
            
            if "news" in result:
                assert isinstance(result["news"], list), "News should be a list"
            
            osint_logger.logger.info("Serper news search test passed")
            
        except Exception as e:
            if "api key" in str(e).lower() or "unauthorized" in str(e).lower():
                pytest.skip("Serper API key not configured")
            elif "rate limit" in str(e).lower() or "quota" in str(e).lower():
                pytest.skip("API rate limit reached")
            else:
                raise
    
    def test_serper_image_search(self):
        """Test Serper image search"""
        
        serper = SerperSearchTool()
        
        try:
            result = serper.search("OSINT logo", search_type="images")
            
            # Validate result
            assert isinstance(result, dict), "Image search result should be a dictionary"
            
            if "images" in result:
                assert isinstance(result["images"], list), "Images should be a list"
            
            osint_logger.logger.info("Serper image search test passed")
            
        except Exception as e:
            if "api key" in str(e).lower() or "unauthorized" in str(e).lower():
                pytest.skip("Serper API key not configured")
            elif "rate limit" in str(e).lower() or "quota" in str(e).lower():
                pytest.skip("API rate limit reached")
            else:
                raise
    
    def test_crawler_initialization(self):
        """Test Crawl4AI wrapper initialization"""
        
        crawler = Crawl4AITool()
        
        # Check initialization
        assert hasattr(crawler, 'crawl_url'), "Crawler should have crawl_url method"
        assert hasattr(crawler, 'batch_crawl'), "Crawler should have batch_crawl method"
        
        osint_logger.logger.info("Crawler initialization test passed")
    
    def test_crawler_basic(self):
        """Test basic crawling functionality"""
        
        crawler = Crawl4AITool()
        
        # Test with a reliable URL
        test_url = "https://httpbin.org/html"
        
        try:
            result = crawler.crawl_url(test_url)
            
            # Validate result structure
            assert isinstance(result, dict), "Crawl result should be a dictionary"
            
            # Check for expected fields
            expected_fields = ["url", "content", "metadata"]
            for field in expected_fields:
                if field in result:
                    assert result[field] is not None, f"{field} should not be None"
            
            # Check URL matches
            if "url" in result:
                assert test_url in result["url"], "Result URL should match requested URL"
            
            osint_logger.logger.info("Basic crawling test passed")
            
        except Exception as e:
            if "connection" in str(e).lower() or "timeout" in str(e).lower():
                pytest.skip("Network connectivity issue")
            else:
                raise
    
    def test_crawler_batch(self):
        """Test batch crawling functionality"""
        
        crawler = Crawl4AITool()
        
        # Test URLs
        test_urls = [
            "https://httpbin.org/html",
            "https://httpbin.org/json",
            "https://example.com"
        ]
        
        try:
            results = crawler.batch_crawl(test_urls, max_concurrent=2)
            
            # Validate results
            assert isinstance(results, list), "Batch crawl should return a list"
            assert len(results) <= len(test_urls), "Should not return more results than URLs"
            
            # Check each result
            for result in results:
                assert isinstance(result, dict), "Each result should be a dictionary"
                if "error" not in result:
                    assert "url" in result, "Successful result should have URL"
            
            osint_logger.logger.info("Batch crawling test passed")
            
        except Exception as e:
            if "connection" in str(e).lower() or "timeout" in str(e).lower():
                pytest.skip("Network connectivity issue")
            else:
                raise
    
    def test_tools_performance(self):
        """Test tools performance"""
        
        def quick_search():
            serper = SerperSearchTool()
            return serper.search("test", search_type="search")
        
        def quick_crawl():
            crawler = Crawl4AITool()
            return crawler.crawl_url("https://httpbin.org/get")
        
        # Test search performance
        try:
            search_perf = measure_performance(quick_search, max_duration=15.0)
            assert search_perf["performance_ok"], f"Search performance test failed: {search_perf.get('error')}"
            osint_logger.logger.info(f"Search performance: {search_perf['duration']:.2f}s")
        except Exception as e:
            if "api key" in str(e).lower():
                pytest.skip("API key not configured for performance test")
            else:
                raise
        
        # Test crawl performance
        try:
            crawl_perf = measure_performance(quick_crawl, max_duration=20.0)
            assert crawl_perf["performance_ok"], f"Crawl performance test failed: {crawl_perf.get('error')}"
            osint_logger.logger.info(f"Crawl performance: {crawl_perf['duration']:.2f}s")
        except Exception as e:
            if "connection" in str(e).lower():
                pytest.skip("Network connectivity issue in performance test")
            else:
                raise
    
    def test_tools_error_handling(self):
        """Test tools error handling"""
        
        serper = SerperSearchTool()
        crawler = Crawl4AITool()
        
        # Test Serper with invalid input
        try:
            result = serper.search("", search_type="invalid_type")
            # Should handle gracefully
            assert isinstance(result, dict), "Should return dict even with invalid input"
        except Exception as e:
            # Should not raise unhandled exceptions for invalid input
            assert "api key" in str(e).lower() or "invalid" in str(e).lower(), f"Unexpected error: {e}"
        
        # Test crawler with invalid URL
        try:
            result = crawler.crawl_url("invalid-url")
            # Should handle gracefully
            assert isinstance(result, dict), "Should return dict even with invalid URL"
            if "error" not in result:
                # If no error field, should have some indication of failure
                assert result.get("content") is None or result.get("content") == "", "Should not return content for invalid URL"
        except Exception as e:
            # Should handle invalid URLs gracefully
            assert "invalid" in str(e).lower() or "url" in str(e).lower(), f"Unexpected error: {e}"
        
        osint_logger.logger.info("Tools error handling test passed")


class SecurityToolsTestSuite(TestSuite):
    """Test suite for security and privacy tools"""
    
    def __init__(self):
        super().__init__("Security Tools Tests")
        self.setup_tests()
    
    def setup_tests(self):
        """Setup security tools tests"""
        
        self.add_test(self.test_encryption_manager, "Encryption Manager")
        self.add_test(self.test_auth_manager, "Authentication Manager")
        self.add_test(self.test_privacy_manager, "Privacy Manager")
        self.add_test(self.test_local_embeddings, "Local Embeddings")
    
    def test_encryption_manager(self):
        """Test encryption manager"""
        
        try:
            from utils.security import encryption_manager
            
            if not encryption_manager:
                pytest.skip("Encryption manager not available")
            
            # Test data encryption
            test_data = "This is sensitive test data"
            encrypted = encryption_manager.encrypt_data(test_data)
            
            assert isinstance(encrypted, str), "Encrypted data should be string"
            assert encrypted != test_data, "Encrypted data should be different from original"
            
            # Test decryption
            decrypted = encryption_manager.decrypt_data(encrypted)
            assert decrypted.decode('utf-8') == test_data, "Decrypted data should match original"
            
            osint_logger.logger.info("Encryption manager test passed")
            
        except ImportError:
            pytest.skip("Cryptography dependencies not available")
    
    def test_auth_manager(self):
        """Test authentication manager"""
        
        try:
            from utils.security import auth_manager
            
            if not auth_manager:
                pytest.skip("Authentication manager not available")
            
            # Test user creation
            test_username = f"test_user_{int(datetime.now().timestamp())}"
            test_password = "test_password_123"
            
            user_id = auth_manager.create_user(
                username=test_username,
                password=test_password,
                permissions=["read", "write"]
            )
            
            assert user_id, "User ID should be created"
            
            # Test authentication
            session_id = auth_manager.authenticate_user(
                username=test_username,
                password=test_password,
                ip_address="127.0.0.1",
                user_agent="test-agent"
            )
            
            assert session_id, "Session ID should be created"
            
            # Test session validation
            session = auth_manager.validate_session(session_id)
            assert session is not None, "Session should be valid"
            assert session.user_id == user_id, "Session should belong to correct user"
            
            osint_logger.logger.info("Authentication manager test passed")
            
        except Exception as e:
            if "already exists" in str(e).lower():
                osint_logger.logger.info("User already exists - test passed")
            else:
                raise
    
    def test_privacy_manager(self):
        """Test privacy manager"""
        
        try:
            from utils.security import privacy_manager
            
            if not privacy_manager:
                pytest.skip("Privacy manager not available")
            
            # Test sensitive data scanning
            test_text = "Contact <NAME_EMAIL> or call ************"
            
            findings = privacy_manager.scan_for_sensitive_data(test_text)
            
            assert isinstance(findings, dict), "Findings should be a dictionary"
            
            # Should detect email and phone
            assert "email" in findings or "phone" in findings, "Should detect sensitive data"
            
            # Test text sanitization
            sanitized = privacy_manager.sanitize_text(test_text)
            
            assert sanitized != test_text, "Sanitized text should be different"
            assert "<EMAIL>" not in sanitized, "Email should be removed"
            
            osint_logger.logger.info("Privacy manager test passed")
            
        except ImportError:
            pytest.skip("Privacy manager dependencies not available")
    
    def test_local_embeddings(self):
        """Test local embeddings"""
        
        try:
            from utils.security import local_embeddings
            
            if not local_embeddings or not local_embeddings.model:
                pytest.skip("Local embeddings not available")
            
            # Test embedding generation
            test_texts = ["This is a test sentence", "Another test sentence"]
            
            embeddings = local_embeddings.generate_embeddings(test_texts)
            
            assert isinstance(embeddings, list), "Embeddings should be a list"
            assert len(embeddings) == 2, "Should generate embeddings for both texts"
            
            # Test similarity computation
            similarity = local_embeddings.compute_similarity(test_texts[0], test_texts[1])
            
            assert isinstance(similarity, float), "Similarity should be a float"
            assert 0 <= similarity <= 1, "Similarity should be between 0 and 1"
            
            osint_logger.logger.info("Local embeddings test passed")
            
        except ImportError:
            pytest.skip("Sentence transformers not available")


class MonitoringTestSuite(TestSuite):
    """Test suite for monitoring and logging"""
    
    def __init__(self):
        super().__init__("Monitoring Tests")
        self.setup_tests()
    
    def setup_tests(self):
        """Setup monitoring tests"""
        
        self.add_test(self.test_health_checker, "Health Checker")
        self.add_test(self.test_metrics_collector, "Metrics Collector")
        self.add_test(self.test_logging_system, "Logging System")
    
    def test_health_checker(self):
        """Test health checking system"""
        
        try:
            from utils.monitoring import health_checker
            
            # Run health checks
            health_result = asyncio.run(health_checker.run_health_checks())
            
            assert isinstance(health_result, dict), "Health result should be a dictionary"
            assert "status" in health_result, "Health result should have status"
            assert "checks" in health_result, "Health result should have checks"
            
            # Status should be valid
            assert health_result["status"] in ["healthy", "unhealthy"], "Status should be valid"
            
            osint_logger.logger.info("Health checker test passed")
            
        except Exception as e:
            osint_logger.logger.warning(f"Health checker test failed: {e}")
            # Don't fail the test for monitoring issues
    
    def test_metrics_collector(self):
        """Test metrics collection"""
        
        try:
            from utils.monitoring import metrics_collector
            
            # Collect metrics
            metrics = metrics_collector.collect_metrics()
            
            assert isinstance(metrics, dict), "Metrics should be a dictionary"
            assert "system" in metrics, "Metrics should have system data"
            assert "application" in metrics, "Metrics should have application data"
            
            # Check system metrics
            system_metrics = metrics["system"]
            assert "cpu_percent" in system_metrics, "Should have CPU metrics"
            assert "memory_percent" in system_metrics, "Should have memory metrics"
            
            osint_logger.logger.info("Metrics collector test passed")
            
        except Exception as e:
            osint_logger.logger.warning(f"Metrics collector test failed: {e}")
            # Don't fail the test for monitoring issues
    
    def test_logging_system(self):
        """Test logging system"""
        
        # Test logging functionality
        test_message = f"Test log message at {datetime.now().isoformat()}"
        
        osint_logger.logger.info(test_message)
        osint_logger.logger.warning("Test warning message")
        osint_logger.logger.error("Test error message")
        
        # Check if log file exists
        log_file = Path("logs/osint_framework.log")
        if log_file.exists():
            # Read recent logs
            with open(log_file, 'r') as f:
                log_content = f.read()
            
            assert test_message in log_content, "Test message should be in log file"
        
        osint_logger.logger.info("Logging system test passed")


# Register test suites
tools_test_suite = ToolsTestSuite()
security_test_suite = SecurityToolsTestSuite()
monitoring_test_suite = MonitoringTestSuite()

test_framework.register_suite(tools_test_suite)
test_framework.register_suite(security_test_suite)
test_framework.register_suite(monitoring_test_suite)
