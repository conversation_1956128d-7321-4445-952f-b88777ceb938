"""
🧠 CrewAI OSINT Agent Framework - Security Module Unit Tests

Comprehensive unit tests for the security module including encryption,
authentication, authorization, and privacy features.

Test Coverage:
    - Encryption/decryption operations
    - Key generation and management
    - Authentication mechanisms
    - Authorization controls
    - Privacy protection features
    - Input validation and sanitization
    - Error handling and edge cases
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

import pytest

from utils.security import (
    EncryptionManager,
    AuthenticationManager,
    PrivacyManager,
    SecurityError,
    AuthenticationError,
    AuthorizationError
)


class TestEncryptionManager:
    """Test cases for EncryptionManager class."""
    
    def test_encryption_manager_initialization(self, mock_env_vars):
        """Test encryption manager initialization."""
        manager = EncryptionManager()
        
        assert manager.master_key is not None
        assert manager.fernet is not None
        assert manager.encryption_stats["operations_count"] == 0
    
    def test_encryption_manager_with_custom_key(self):
        """Test encryption manager with custom master key."""
        from cryptography.fernet import Fernet
        custom_key = Fernet.generate_key().decode()
        
        manager = EncryptionManager(master_key=custom_key)
        assert manager.master_key == custom_key
    
    def test_data_encryption_decryption(self, mock_env_vars):
        """Test basic data encryption and decryption."""
        manager = EncryptionManager()
        test_data = "This is sensitive test data"
        
        # Encrypt data
        encrypted = manager.encrypt_data(test_data)
        assert encrypted != test_data
        assert isinstance(encrypted, str)
        
        # Decrypt data
        decrypted = manager.decrypt_data(encrypted)
        assert decrypted == test_data.encode()
    
    def test_file_encryption_decryption(self, mock_env_vars, temp_dir):
        """Test file encryption and decryption."""
        manager = EncryptionManager()
        
        # Create test file
        test_file = temp_dir / "test_file.txt"
        test_content = "This is test file content for encryption"
        test_file.write_text(test_content)
        
        # Encrypt file
        encrypted_file = manager.encrypt_file(str(test_file))
        assert Path(encrypted_file).exists()
        assert encrypted_file.endswith(".enc")
        
        # Decrypt file
        decrypted_file = manager.decrypt_file(encrypted_file)
        assert Path(decrypted_file).read_text() == test_content
    
    def test_encryption_with_invalid_data(self, mock_env_vars):
        """Test encryption with invalid data types."""
        manager = EncryptionManager()
        
        with pytest.raises(TypeError):
            manager.encrypt_data(None)
        
        with pytest.raises(TypeError):
            manager.encrypt_data(123)
    
    def test_decryption_with_invalid_data(self, mock_env_vars):
        """Test decryption with invalid encrypted data."""
        manager = EncryptionManager()
        
        with pytest.raises(Exception):
            manager.decrypt_data("invalid_encrypted_data")
    
    def test_key_validation(self):
        """Test key format validation."""
        manager = EncryptionManager()
        
        # Test valid key
        from cryptography.fernet import Fernet
        valid_key = Fernet.generate_key().decode()
        manager._validate_key_format(valid_key)  # Should not raise
        
        # Test invalid key
        with pytest.raises(SecurityError):
            manager._validate_key_format("invalid_key")
    
    def test_ssrf_protection(self, mock_env_vars):
        """Test SSRF protection in URL validation."""
        # This would be in crawl4ai_wrapper, but testing the concept
        from tools.crawl4ai_wrapper import Crawl4AIWrapper
        
        wrapper = Crawl4AIWrapper()
        
        # Test valid URLs
        wrapper._validate_url("https://example.com")
        wrapper._validate_url("http://public-site.org")
        
        # Test invalid URLs (should raise ValueError)
        with pytest.raises(ValueError):
            wrapper._validate_url("http://localhost:8080")
        
        with pytest.raises(ValueError):
            wrapper._validate_url("https://127.0.0.1")
        
        with pytest.raises(ValueError):
            wrapper._validate_url("http://***********")


class TestAuthenticationManager:
    """Test cases for AuthenticationManager class."""
    
    def test_authentication_manager_initialization(self, mock_env_vars):
        """Test authentication manager initialization."""
        manager = AuthenticationManager()
        
        assert manager.secret_key is not None
        assert manager.algorithm == "HS256"
        assert manager.access_token_expire_minutes == 30
    
    def test_user_creation(self, mock_env_vars):
        """Test user creation functionality."""
        manager = AuthenticationManager()
        
        user_data = manager.create_user(
            username="testuser",
            password="testpassword123",
            permissions=["read", "write"]
        )
        
        assert user_data["username"] == "testuser"
        assert "password_hash" in user_data
        assert user_data["permissions"] == ["read", "write"]
        assert user_data["password_hash"] != "testpassword123"
    
    def test_password_verification(self, mock_env_vars):
        """Test password verification."""
        manager = AuthenticationManager()
        
        # Create user
        user_data = manager.create_user("testuser", "testpassword123")
        
        # Test correct password
        assert manager.verify_password("testpassword123", user_data["password_hash"])
        
        # Test incorrect password
        assert not manager.verify_password("wrongpassword", user_data["password_hash"])
    
    def test_token_generation_and_verification(self, mock_env_vars):
        """Test JWT token generation and verification."""
        manager = AuthenticationManager()
        
        user_data = {"user_id": "test123", "username": "testuser", "permissions": ["read"]}
        
        # Generate token
        token = manager.create_access_token(user_data)
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token
        decoded_data = manager.verify_token(token)
        assert decoded_data["user_id"] == "test123"
        assert decoded_data["username"] == "testuser"
    
    def test_invalid_token_verification(self, mock_env_vars):
        """Test verification of invalid tokens."""
        manager = AuthenticationManager()
        
        with pytest.raises(AuthenticationError):
            manager.verify_token("invalid.token.here")
    
    def test_expired_token_verification(self, mock_env_vars):
        """Test verification of expired tokens."""
        manager = AuthenticationManager()
        
        # Create token with very short expiration
        user_data = {"user_id": "test123"}
        
        with patch('utils.security.datetime') as mock_datetime:
            # Mock current time for token creation
            mock_datetime.utcnow.return_value = MagicMock()
            mock_datetime.utcnow.return_value.timestamp.return_value = 1000000
            
            token = manager.create_access_token(user_data, expires_delta_minutes=0.01)
            
            # Mock time advancement
            mock_datetime.utcnow.return_value.timestamp.return_value = 1000060  # 1 minute later
            
            with pytest.raises(AuthenticationError):
                manager.verify_token(token)


class TestPrivacyManager:
    """Test cases for PrivacyManager class."""
    
    def test_privacy_manager_initialization(self, mock_env_vars):
        """Test privacy manager initialization."""
        manager = PrivacyManager()
        
        assert manager.patterns is not None
        assert len(manager.patterns) > 0
    
    def test_sensitive_data_detection(self, mock_env_vars):
        """Test sensitive data detection."""
        manager = PrivacyManager()
        
        test_text = """
        Contact John <NAME_EMAIL> or call ************.
        His SSN is *********** and credit card is 4532-1234-5678-9012.
        """
        
        findings = manager.scan_for_sensitive_data(test_text)
        
        # Should find email, phone, SSN, and credit card
        assert len(findings) >= 4
        
        # Check specific findings
        finding_types = [f["type"] for f in findings]
        assert "email" in finding_types
        assert "phone" in finding_types
        assert "ssn" in finding_types
        assert "credit_card" in finding_types
    
    def test_text_sanitization(self, mock_env_vars):
        """Test text sanitization functionality."""
        manager = PrivacyManager()
        
        test_text = "Contact <NAME_EMAIL> or ************"
        sanitized = manager.sanitize_text(test_text)
        
        # Should not contain original sensitive data
        assert "<EMAIL>" not in sanitized
        assert "************" not in sanitized
        assert "[REDACTED]" in sanitized
    
    def test_custom_sanitization_replacement(self, mock_env_vars):
        """Test custom replacement text in sanitization."""
        manager = PrivacyManager()
        
        test_text = "Email: <EMAIL>"
        sanitized = manager.sanitize_text(test_text, replacement="[HIDDEN]")
        
        assert "[HIDDEN]" in sanitized
        assert "<EMAIL>" not in sanitized
    
    def test_no_sensitive_data(self, mock_env_vars):
        """Test text with no sensitive data."""
        manager = PrivacyManager()
        
        clean_text = "This is a normal text without any sensitive information."
        findings = manager.scan_for_sensitive_data(clean_text)
        sanitized = manager.sanitize_text(clean_text)
        
        assert len(findings) == 0
        assert sanitized == clean_text


class TestSecurityIntegration:
    """Integration tests for security components."""
    
    def test_end_to_end_encryption_workflow(self, mock_env_vars, temp_dir):
        """Test complete encryption workflow."""
        manager = EncryptionManager()
        
        # Test data
        sensitive_data = {
            "user_id": "12345",
            "analysis_results": "Confidential OSINT findings",
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        # Encrypt
        encrypted = manager.encrypt_data(str(sensitive_data))
        
        # Decrypt
        decrypted = manager.decrypt_data(encrypted)
        
        # Verify
        assert str(sensitive_data).encode() == decrypted
    
    def test_security_error_handling(self, mock_env_vars):
        """Test security error handling."""
        # Test with missing cryptography library
        with patch('utils.security.CRYPTOGRAPHY_AVAILABLE', False):
            with pytest.raises(ImportError):
                EncryptionManager()
    
    def test_performance_requirements(self, mock_env_vars, performance_timer):
        """Test that security operations meet performance requirements."""
        manager = EncryptionManager()
        test_data = "A" * 1000  # 1KB of data
        
        # Test encryption performance
        performance_timer.start()
        encrypted = manager.encrypt_data(test_data)
        encrypt_time = performance_timer.stop()
        
        # Test decryption performance
        performance_timer.start()
        decrypted = manager.decrypt_data(encrypted)
        decrypt_time = performance_timer.stop()
        
        # Should complete within reasonable time (adjust as needed)
        assert encrypt_time < 1.0, f"Encryption took {encrypt_time}s, expected < 1.0s"
        assert decrypt_time < 1.0, f"Decryption took {decrypt_time}s, expected < 1.0s"
        
        # Verify correctness
        assert decrypted == test_data.encode()
