"""
🧠 CrewAI OSINT Agent Framework - Browser Automation

Advanced browser automation for OSINT operations using Selenium, Playwright,
and undetected Chrome for stealth web scraping and interaction.
"""

import asyncio
import base64
import json
import sys
import tempfile
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Browser automation imports
try:
    from selenium import webdriver
    from selenium.common.exceptions import TimeoutException, WebDriverException
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.common.by import By
    from selenium.webdriver.firefox.options import Options as FirefoxOptions
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.support.ui import WebDriverWait

    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    from playwright.async_api import async_playwright
    from playwright.sync_api import sync_playwright

    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

try:
    import undetected_chromedriver as uc

    UNDETECTED_CHROME_AVAILABLE = True
except ImportError:
    UNDETECTED_CHROME_AVAILABLE = False

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.logging_config import log_execution_time, osint_logger


class BrowserAutomation:
    """Advanced browser automation for OSINT operations"""

    def __init__(self, browser_type: str = "chrome", headless: bool = True, stealth: bool = False):
        self.browser_type = browser_type
        self.headless = headless
        self.stealth = stealth
        self.driver = None
        self.playwright_browser = None
        self.playwright_context = None

        self.setup_browser()

        osint_logger.logger.info(
            "Browser automation initialized",
            browser_type=browser_type,
            headless=headless,
            stealth=stealth,
        )

    def setup_browser(self):
        """Setup browser based on configuration"""

        if self.stealth and UNDETECTED_CHROME_AVAILABLE:
            self._setup_undetected_chrome()
        elif SELENIUM_AVAILABLE:
            self._setup_selenium()
        else:
            osint_logger.logger.warning("No browser automation libraries available")

    def _setup_undetected_chrome(self):
        """Setup undetected Chrome for stealth operations"""

        try:
            options = uc.ChromeOptions()

            if self.headless:
                options.add_argument("--headless")

            # Stealth options
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option("useAutomationExtension", False)

            # User agent rotation
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            ]

            import random

            options.add_argument(f"--user-agent={random.choice(user_agents)}")

            self.driver = uc.Chrome(options=options)

            # Execute script to remove webdriver property
            self.driver.execute_script(
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
            )

            osint_logger.logger.info("Undetected Chrome browser initialized")

        except Exception as e:
            osint_logger.log_error(e, "undetected_chrome_setup")
            self._setup_selenium()  # Fallback

    def _setup_selenium(self):
        """Setup standard Selenium browser"""

        try:
            if self.browser_type.lower() == "chrome":
                options = ChromeOptions()

                if self.headless:
                    options.add_argument("--headless")

                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")
                options.add_argument("--disable-gpu")
                options.add_argument("--window-size=1920,1080")

                self.driver = webdriver.Chrome(options=options)

            elif self.browser_type.lower() == "firefox":
                options = FirefoxOptions()

                if self.headless:
                    options.add_argument("--headless")

                self.driver = webdriver.Firefox(options=options)

            else:
                raise ValueError(f"Unsupported browser type: {self.browser_type}")

            osint_logger.logger.info(f"Selenium {self.browser_type} browser initialized")

        except Exception as e:
            osint_logger.log_error(e, "selenium_setup")
            raise

    @log_execution_time("browser_automation")
    def scrape_page(
        self, url: str, wait_time: int = 10, extract_elements: List[str] = None
    ) -> Dict[str, Any]:
        """Scrape a web page with advanced capabilities"""

        result = {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "page_data": {},
            "elements": {},
            "metadata": {},
            "screenshots": [],
        }

        if not self.driver:
            result["error"] = "Browser not initialized"
            return result

        try:
            # Navigate to page
            self.driver.get(url)

            # Wait for page load
            WebDriverWait(self.driver, wait_time).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Basic page data
            result["page_data"] = {
                "title": self.driver.title,
                "current_url": self.driver.current_url,
                "page_source_length": len(self.driver.page_source),
            }

            # Extract specific elements
            if extract_elements:
                result["elements"] = self._extract_elements(extract_elements)

            # Page metadata
            result["metadata"] = self._extract_page_metadata()

            # Take screenshot
            screenshot_data = self._take_screenshot()
            if screenshot_data:
                result["screenshots"].append(screenshot_data)

            # Check for anti-bot measures
            result["anti_bot_detection"] = self._detect_anti_bot_measures()

            osint_logger.logger.info(
                "Page scraped successfully", url=url, title=result["page_data"]["title"][:50]
            )

        except TimeoutException:
            result["error"] = "Page load timeout"
        except WebDriverException as e:
            result["error"] = f"WebDriver error: {str(e)}"
        except Exception as e:
            result["error"] = str(e)
            osint_logger.log_error(e, "page_scraping", {"url": url})

        return result

    def _extract_elements(self, selectors: List[str]) -> Dict[str, Any]:
        """Extract specific elements from the page"""

        elements = {}

        for selector in selectors:
            try:
                if selector.startswith("//"):
                    # XPath selector
                    element_list = self.driver.find_elements(By.XPATH, selector)
                elif selector.startswith("#"):
                    # ID selector
                    element_list = self.driver.find_elements(By.ID, selector[1:])
                elif selector.startswith("."):
                    # Class selector
                    element_list = self.driver.find_elements(By.CLASS_NAME, selector[1:])
                else:
                    # CSS selector
                    element_list = self.driver.find_elements(By.CSS_SELECTOR, selector)

                elements[selector] = []
                for element in element_list[:10]:  # Limit to 10 elements
                    element_data = {
                        "text": element.text,
                        "tag_name": element.tag_name,
                        "attributes": {},
                    }

                    # Get common attributes
                    for attr in ["id", "class", "href", "src", "alt", "title"]:
                        value = element.get_attribute(attr)
                        if value:
                            element_data["attributes"][attr] = value

                    elements[selector].append(element_data)

            except Exception as e:
                elements[selector] = {"error": str(e)}

        return elements

    def _extract_page_metadata(self) -> Dict[str, Any]:
        """Extract page metadata"""

        metadata = {}

        try:
            # Meta tags
            meta_tags = self.driver.find_elements(By.TAG_NAME, "meta")
            metadata["meta_tags"] = {}

            for meta in meta_tags[:20]:  # Limit to 20 meta tags
                name = meta.get_attribute("name") or meta.get_attribute("property")
                content = meta.get_attribute("content")

                if name and content:
                    metadata["meta_tags"][name] = content

            # Links
            links = self.driver.find_elements(By.TAG_NAME, "a")
            metadata["links"] = {
                "total_count": len(links),
                "external_links": [],
                "internal_links": [],
            }

            current_domain = (
                self.driver.current_url.split("/")[2] if "/" in self.driver.current_url else ""
            )

            for link in links[:50]:  # Limit to 50 links
                href = link.get_attribute("href")
                text = link.text.strip()

                if href and text:
                    link_data = {"href": href, "text": text[:100]}

                    if current_domain in href or href.startswith("/"):
                        metadata["links"]["internal_links"].append(link_data)
                    else:
                        metadata["links"]["external_links"].append(link_data)

            # Images
            images = self.driver.find_elements(By.TAG_NAME, "img")
            metadata["images"] = {
                "total_count": len(images),
                "images_with_alt": sum(1 for img in images if img.get_attribute("alt")),
                "sample_images": [],
            }

            for img in images[:10]:  # Sample of 10 images
                src = img.get_attribute("src")
                alt = img.get_attribute("alt")

                if src:
                    metadata["images"]["sample_images"].append({"src": src, "alt": alt or ""})

            # Forms
            forms = self.driver.find_elements(By.TAG_NAME, "form")
            metadata["forms"] = {"total_count": len(forms), "form_details": []}

            for form in forms:
                action = form.get_attribute("action")
                method = form.get_attribute("method")

                inputs = form.find_elements(By.TAG_NAME, "input")
                input_types = [inp.get_attribute("type") for inp in inputs]

                metadata["forms"]["form_details"].append(
                    {
                        "action": action,
                        "method": method,
                        "input_count": len(inputs),
                        "input_types": input_types,
                    }
                )

        except Exception as e:
            metadata["error"] = str(e)

        return metadata

    def _take_screenshot(self) -> Optional[Dict[str, Any]]:
        """Take a screenshot of the current page"""

        try:
            screenshot_data = self.driver.get_screenshot_as_base64()

            return {
                "format": "base64_png",
                "data": screenshot_data,
                "timestamp": datetime.now().isoformat(),
                "viewport_size": self.driver.get_window_size(),
            }

        except Exception as e:
            osint_logger.logger.warning(f"Failed to take screenshot: {e}")
            return None

    def _detect_anti_bot_measures(self) -> Dict[str, Any]:
        """Detect potential anti-bot measures"""

        detection = {
            "cloudflare_detected": False,
            "captcha_detected": False,
            "rate_limiting": False,
            "javascript_challenges": False,
            "indicators": [],
        }

        try:
            page_source = self.driver.page_source.lower()

            # Cloudflare detection
            cloudflare_indicators = ["cloudflare", "cf-ray", "checking your browser"]
            if any(indicator in page_source for indicator in cloudflare_indicators):
                detection["cloudflare_detected"] = True
                detection["indicators"].append("Cloudflare protection detected")

            # CAPTCHA detection
            captcha_indicators = ["captcha", "recaptcha", "hcaptcha"]
            if any(indicator in page_source for indicator in captcha_indicators):
                detection["captcha_detected"] = True
                detection["indicators"].append("CAPTCHA challenge detected")

            # Rate limiting
            rate_limit_indicators = ["rate limit", "too many requests", "429"]
            if any(indicator in page_source for indicator in rate_limit_indicators):
                detection["rate_limiting"] = True
                detection["indicators"].append("Rate limiting detected")

            # JavaScript challenges
            js_challenge_indicators = ["javascript required", "enable javascript", "js challenge"]
            if any(indicator in page_source for indicator in js_challenge_indicators):
                detection["javascript_challenges"] = True
                detection["indicators"].append("JavaScript challenge detected")

        except Exception as e:
            detection["error"] = str(e)

        return detection

    def interact_with_page(self, interactions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Perform interactions with the page"""

        result = {"interactions_performed": [], "errors": [], "final_state": {}}

        if not self.driver:
            result["errors"].append("Browser not initialized")
            return result

        for i, interaction in enumerate(interactions):
            try:
                action = interaction.get("action")
                selector = interaction.get("selector")
                value = interaction.get("value")
                wait_time = interaction.get("wait_time", 5)

                if action == "click":
                    element = WebDriverWait(self.driver, wait_time).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    element.click()

                elif action == "type":
                    element = WebDriverWait(self.driver, wait_time).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    element.clear()
                    element.send_keys(value)

                elif action == "wait":
                    time.sleep(value)

                elif action == "scroll":
                    self.driver.execute_script(f"window.scrollBy(0, {value});")

                elif action == "screenshot":
                    screenshot = self._take_screenshot()
                    result["interactions_performed"].append(
                        {"action": action, "screenshot": screenshot}
                    )
                    continue

                result["interactions_performed"].append(
                    {"action": action, "selector": selector, "value": value, "success": True}
                )

            except Exception as e:
                result["errors"].append(
                    {"interaction_index": i, "action": interaction.get("action"), "error": str(e)}
                )

        # Get final page state
        try:
            result["final_state"] = {
                "url": self.driver.current_url,
                "title": self.driver.title,
                "page_source_length": len(self.driver.page_source),
            }
        except:
            pass

        return result

    def close(self):
        """Close the browser"""

        try:
            if self.driver:
                self.driver.quit()
                self.driver = None

            if self.playwright_browser:
                asyncio.run(self.playwright_browser.close())
                self.playwright_browser = None

            osint_logger.logger.info("Browser closed")

        except Exception as e:
            osint_logger.log_error(e, "browser_close")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class PlaywrightAutomation:
    """Advanced browser automation using Playwright"""

    def __init__(self, browser_type: str = "chromium", headless: bool = True):
        self.browser_type = browser_type
        self.headless = headless
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None

        if not PLAYWRIGHT_AVAILABLE:
            raise ImportError("Playwright not available")

        osint_logger.logger.info(
            "Playwright automation initialized", browser_type=browser_type, headless=headless
        )

    async def start(self):
        """Start Playwright browser"""

        try:
            self.playwright = await async_playwright().start()

            # Launch browser
            if self.browser_type == "chromium":
                self.browser = await self.playwright.chromium.launch(headless=self.headless)
            elif self.browser_type == "firefox":
                self.browser = await self.playwright.firefox.launch(headless=self.headless)
            elif self.browser_type == "webkit":
                self.browser = await self.playwright.webkit.launch(headless=self.headless)
            else:
                raise ValueError(f"Unsupported browser type: {self.browser_type}")

            # Create context with stealth settings
            self.context = await self.browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            )

            # Create page
            self.page = await self.context.new_page()

            osint_logger.logger.info("Playwright browser started")

        except Exception as e:
            osint_logger.log_error(e, "playwright_start")
            raise

    async def scrape_page_advanced(
        self, url: str, wait_for_selector: str = None, wait_time: int = 30000
    ) -> Dict[str, Any]:
        """Advanced page scraping with Playwright"""

        result = {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "page_data": {},
            "performance": {},
            "network_requests": [],
            "console_logs": [],
            "screenshots": [],
        }

        if not self.page:
            await self.start()

        try:
            # Set up request/response monitoring
            requests = []

            async def handle_request(request):
                requests.append(
                    {
                        "url": request.url,
                        "method": request.method,
                        "headers": dict(request.headers),
                        "timestamp": datetime.now().isoformat(),
                    }
                )

            async def handle_response(response):
                for req in requests:
                    if req["url"] == response.url:
                        req["status"] = response.status
                        req["response_headers"] = dict(response.headers)
                        break

            self.page.on("request", handle_request)
            self.page.on("response", handle_response)

            # Set up console monitoring
            console_logs = []

            def handle_console(msg):
                console_logs.append(
                    {"type": msg.type, "text": msg.text, "timestamp": datetime.now().isoformat()}
                )

            self.page.on("console", handle_console)

            # Navigate to page
            start_time = time.time()
            await self.page.goto(url, wait_until="networkidle", timeout=wait_time)
            load_time = time.time() - start_time

            # Wait for specific selector if provided
            if wait_for_selector:
                await self.page.wait_for_selector(wait_for_selector, timeout=wait_time)

            # Extract page data
            result["page_data"] = {
                "title": await self.page.title(),
                "url": self.page.url,
                "content_length": len(await self.page.content()),
            }

            # Performance metrics
            result["performance"] = {
                "load_time_seconds": load_time,
                "network_requests_count": len(requests),
            }

            # Network requests
            result["network_requests"] = requests

            # Console logs
            result["console_logs"] = console_logs

            # Take screenshot
            screenshot_buffer = await self.page.screenshot(full_page=True)
            result["screenshots"].append(
                {
                    "type": "full_page",
                    "data": base64.b64encode(screenshot_buffer).decode(),
                    "timestamp": datetime.now().isoformat(),
                }
            )

            osint_logger.logger.info(
                "Advanced page scraping completed",
                url=url,
                load_time=load_time,
                requests_count=len(requests),
            )

        except Exception as e:
            result["error"] = str(e)
            osint_logger.log_error(e, "playwright_scraping", {"url": url})

        return result

    async def extract_dynamic_content(
        self, url: str, selectors: Dict[str, str], wait_time: int = 30000
    ) -> Dict[str, Any]:
        """Extract dynamic content that requires JavaScript execution"""

        result = {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "extracted_data": {},
            "execution_info": {},
        }

        if not self.page:
            await self.start()

        try:
            # Navigate to page
            await self.page.goto(url, wait_until="networkidle", timeout=wait_time)

            # Wait for page to be fully loaded
            await self.page.wait_for_load_state("domcontentloaded")

            # Extract data using selectors
            for key, selector in selectors.items():
                try:
                    # Wait for element to be present
                    await self.page.wait_for_selector(selector, timeout=10000)

                    # Extract element data
                    elements = await self.page.query_selector_all(selector)

                    extracted_elements = []
                    for element in elements[:20]:  # Limit to 20 elements
                        element_data = {
                            "text": await element.text_content(),
                            "inner_html": await element.inner_html(),
                            "attributes": {},
                        }

                        # Get attributes
                        for attr in ["id", "class", "href", "src", "data-*"]:
                            try:
                                value = await element.get_attribute(attr)
                                if value:
                                    element_data["attributes"][attr] = value
                            except:
                                pass

                        extracted_elements.append(element_data)

                    result["extracted_data"][key] = extracted_elements

                except Exception as e:
                    result["extracted_data"][key] = {"error": str(e)}

            # Get page execution info
            result["execution_info"] = {
                "javascript_enabled": await self.page.evaluate(
                    "() => typeof window !== 'undefined'"
                ),
                "page_ready_state": await self.page.evaluate("() => document.readyState"),
                "viewport": await self.page.viewport_size(),
            }

        except Exception as e:
            result["error"] = str(e)
            osint_logger.log_error(e, "dynamic_content_extraction", {"url": url})

        return result

    async def perform_social_media_scraping(
        self, platform: str, url: str, scroll_count: int = 3
    ) -> Dict[str, Any]:
        """Specialized scraping for social media platforms"""

        result = {
            "platform": platform,
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "posts": [],
            "metadata": {},
            "challenges": [],
        }

        if not self.page:
            await self.start()

        try:
            # Platform-specific configurations
            if platform.lower() == "twitter":
                await self._scrape_twitter(url, result, scroll_count)
            elif platform.lower() == "linkedin":
                await self._scrape_linkedin(url, result, scroll_count)
            elif platform.lower() == "facebook":
                await self._scrape_facebook(url, result, scroll_count)
            else:
                result["error"] = f"Unsupported platform: {platform}"

        except Exception as e:
            result["error"] = str(e)
            osint_logger.log_error(e, "social_media_scraping", {"platform": platform, "url": url})

        return result

    async def _scrape_twitter(self, url: str, result: Dict[str, Any], scroll_count: int):
        """Scrape Twitter content"""

        await self.page.goto(url, wait_until="networkidle")

        # Check for login requirement
        if "login" in self.page.url:
            result["challenges"].append("Login required")
            return

        # Wait for tweets to load
        try:
            await self.page.wait_for_selector('[data-testid="tweet"]', timeout=10000)
        except:
            result["challenges"].append("No tweets found or page structure changed")
            return

        # Scroll to load more content
        for i in range(scroll_count):
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await self.page.wait_for_timeout(2000)

        # Extract tweets
        tweets = await self.page.query_selector_all('[data-testid="tweet"]')

        for tweet in tweets[:50]:  # Limit to 50 tweets
            try:
                tweet_data = {
                    "text": (
                        await tweet.query_selector('[data-testid="tweetText"]').text_content()
                        if await tweet.query_selector('[data-testid="tweetText"]')
                        else ""
                    ),
                    "author": (
                        await tweet.query_selector('[data-testid="User-Name"]').text_content()
                        if await tweet.query_selector('[data-testid="User-Name"]')
                        else ""
                    ),
                    "timestamp": (
                        await tweet.query_selector("time").get_attribute("datetime")
                        if await tweet.query_selector("time")
                        else ""
                    ),
                    "metrics": {},
                }

                # Extract metrics (likes, retweets, etc.)
                metrics_elements = await tweet.query_selector_all(
                    '[role="group"] [data-testid*="like"], [role="group"] [data-testid*="retweet"]'
                )
                for metric in metrics_elements:
                    metric_text = await metric.text_content()
                    if metric_text:
                        tweet_data["metrics"][
                            await metric.get_attribute("data-testid")
                        ] = metric_text

                result["posts"].append(tweet_data)

            except Exception as e:
                continue

    async def _scrape_linkedin(self, url: str, result: Dict[str, Any], scroll_count: int):
        """Scrape LinkedIn content"""

        await self.page.goto(url, wait_until="networkidle")

        # Check for login requirement
        if "authwall" in self.page.url or "login" in self.page.url:
            result["challenges"].append("Login required")
            return

        # LinkedIn-specific scraping logic would go here
        result["challenges"].append("LinkedIn scraping requires authentication")

    async def _scrape_facebook(self, url: str, result: Dict[str, Any], scroll_count: int):
        """Scrape Facebook content"""

        await self.page.goto(url, wait_until="networkidle")

        # Check for login requirement
        if "login" in self.page.url:
            result["challenges"].append("Login required")
            return

        # Facebook-specific scraping logic would go here
        result["challenges"].append("Facebook scraping requires authentication")

    async def close(self):
        """Close Playwright browser"""

        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()

            osint_logger.logger.info("Playwright browser closed")

        except Exception as e:
            osint_logger.log_error(e, "playwright_close")

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
