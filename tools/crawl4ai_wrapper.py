"""
Crawl4AI wrapper for structured web crawling and content extraction.

This module provides comprehensive web crawling capabilities using Crawl4AI
for OSINT intelligence gathering. It includes specialized crawlers for different
content types (news, general web pages) with keyword-focused extraction,
structured data processing, and credibility assessment.
"""

import asyncio
import ipaddress
import json
import logging
import re
import socket
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse

from crewai.tools import BaseTool
from pydantic import BaseModel, Field, validator

try:
    from crawl4ai import AsyncWebCrawler

    CRAWL4AI_AVAILABLE = True
except ImportError:
    CRAWL4AI_AVAILABLE = False
    AsyncWebCrawler = None


class Crawl4AIInput(BaseModel):
    """
    Input schema for Crawl4AI tool with comprehensive validation.

    Attributes:
        url: Target URL to crawl and extract content from
        focus_keywords: Keywords to focus extraction on for relevance
        extract_links: Whether to extract and categorize links
        extract_images: Whether to extract image metadata
        max_depth: Maximum crawl depth for recursive crawling
    """

    url: str = Field(description="URL to crawl and extract content from", min_length=1)
    focus_keywords: List[str] = Field(
        default=[], description="Keywords to focus extraction on for enhanced relevance"
    )
    extract_links: bool = Field(
        default=True, description="Whether to extract and categorize internal/external links"
    )
    extract_images: bool = Field(
        default=False, description="Whether to extract image metadata and information"
    )
    max_depth: int = Field(
        default=1, description="Maximum crawl depth for recursive crawling", ge=1, le=3
    )

    @validator("url")
    def validate_url(cls, v: str) -> str:
        """Validate URL format and scheme."""
        if not v.startswith(("http://", "https://")):
            raise ValueError("URL must start with http:// or https://")

        parsed = urlparse(v)
        if not parsed.netloc:
            raise ValueError("URL must have a valid domain")

        return v

    @validator("focus_keywords")
    def validate_keywords(cls, v: List[str]) -> List[str]:
        """Validate and clean focus keywords."""
        if len(v) > 20:
            raise ValueError("Maximum 20 focus keywords allowed")

        # Clean and filter keywords
        cleaned = [kw.strip().lower() for kw in v if kw.strip()]
        return list(set(cleaned))  # Remove duplicates


class Crawl4AITool(BaseTool):
    """
    CrewAI tool wrapper for Crawl4AI web crawling with advanced features.

    This tool provides comprehensive web crawling capabilities with keyword-focused
    content extraction, structured data processing, and intelligent content analysis
    for OSINT intelligence gathering. It includes robust error handling, content
    validation, and specialized extraction strategies.

    Features:
        - Keyword-focused content extraction
        - Link categorization (internal/external)
        - Image metadata extraction
        - Content credibility assessment
        - Structured data processing
        - Async crawling with timeout handling

    Attributes:
        name: Tool identifier for CrewAI
        description: Tool description for LLM understanding
        args_schema: Pydantic schema for input validation
    """

    name: str = "crawl4ai_web_crawler"
    description: str = (
        """Crawl and extract structured content from web pages with advanced analysis. Useful for gathering detailed information from websites, news articles, blogs, and other web sources. Features keyword-focused extraction, link analysis, and content credibility assessment."""
    )
    args_schema: type[BaseModel] = Crawl4AIInput
    logger: Optional[logging.Logger] = None

    def __init__(self, focus_keywords: Optional[List[str]] = None) -> None:
        """
        Initialize the Crawl4AI tool with optional default keywords.

        Args:
            focus_keywords: Default keywords to focus on during extraction

        Raises:
            ImportError: If Crawl4AI is not available
        """
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self._default_focus_keywords = focus_keywords or []

        # Crawling configuration
        self._timeout = 30
        self._max_retries = 2
        self._user_agent = "OSINT-Agent/1.0 (Intelligence Gathering Bot)"

        if not CRAWL4AI_AVAILABLE:
            raise ImportError("Crawl4AI is not available. Install it with: pip install crawl4ai")

        self.logger.info("Crawl4AI tool initialized successfully")

    def _run(
        self,
        url: str,
        focus_keywords: Optional[List[str]] = None,
        extract_links: bool = True,
        extract_images: bool = False,
        max_depth: int = 1,
    ) -> str:
        """
        Crawl a web page and extract structured content with comprehensive error handling.

        Args:
            url: URL to crawl and analyze
            focus_keywords: Keywords to focus extraction on for relevance
            extract_links: Whether to extract and categorize links
            extract_images: Whether to extract image metadata
            max_depth: Maximum crawl depth for recursive crawling

        Returns:
            JSON string containing structured crawl results with metadata

        Note:
            Returns error information as JSON if crawling fails
        """
        self.logger.info(f"Starting crawl for URL: {url}")

        try:
            # Validate URL before crawling
            self._validate_url(url)

            # Use default keywords if none provided
            keywords = focus_keywords or self._default_focus_keywords
            self.logger.info(f"Using {len(keywords)} focus keywords")

            # Run async crawling in sync context with proper cleanup
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                result = loop.run_until_complete(
                    self._async_crawl(url, keywords, extract_links, extract_images, max_depth)
                )

                self.logger.info(f"Crawl completed successfully for {url}")
                return json.dumps(result, indent=2, ensure_ascii=False)

            finally:
                loop.close()

        except ValueError as e:
            error_result = self._create_error_result(url, f"Validation error: {str(e)}")
            return json.dumps(error_result, indent=2)
        except asyncio.TimeoutError:
            error_result = self._create_error_result(
                url, f"Crawl timed out after {self._timeout} seconds"
            )
            return json.dumps(error_result, indent=2)
        except Exception as e:
            self.logger.error(f"Crawl failed for {url}: {str(e)}")
            error_result = self._create_error_result(url, f"Crawl error: {str(e)}")
            return json.dumps(error_result, indent=2)

    def _validate_url(self, url: str) -> None:
        """
        Validate URL before crawling with enhanced SSRF protection.

        Args:
            url: URL to validate

        Raises:
            ValueError: If URL is invalid or potentially unsafe
        """
        parsed = urlparse(url)

        if parsed.scheme not in ["http", "https"]:
            raise ValueError("Only HTTP and HTTPS URLs are supported")

        if not parsed.netloc:
            raise ValueError("URL must have a valid domain")

        # Enhanced SSRF protection
        self._check_ssrf_protection(parsed.netloc)

    def _check_ssrf_protection(self, hostname: str) -> None:
        """
        Enhanced SSRF protection to prevent access to internal resources.

        Args:
            hostname: Hostname to validate

        Raises:
            ValueError: If hostname is potentially unsafe
        """
        # Remove port if present
        host = hostname.split(":")[0].lower()

        # Block localhost variations
        localhost_patterns = [
            "localhost",
            "127.0.0.1",
            "0.0.0.0",
            "::1",
            "0000:0000:0000:0000:0000:0000:0000:0001",
        ]
        if host in localhost_patterns:
            raise ValueError("Access to localhost is not allowed for security reasons")

        # Block private IP ranges
        try:
            ip = ipaddress.ip_address(host)
            if ip.is_private or ip.is_loopback or ip.is_link_local:
                raise ValueError(f"Access to private/internal IP {host} is not allowed")
        except ipaddress.AddressValueError:
            # Not an IP address, continue with hostname checks
            pass

        # Block internal/private hostnames
        internal_patterns = [
            "internal",
            "intranet",
            "private",
            "local",
            "corp",
            "admin",
            "test",
            "staging",
            "dev",
            "development",
        ]
        if any(pattern in host for pattern in internal_patterns):
            self.logger.warning(f"Potentially internal hostname detected: {host}")

        # Resolve hostname to check for private IPs
        try:
            resolved_ips = socket.getaddrinfo(host, None)
            for family, type, proto, canonname, sockaddr in resolved_ips:
                ip_str = sockaddr[0]
                try:
                    ip = ipaddress.ip_address(ip_str)
                    if ip.is_private or ip.is_loopback or ip.is_link_local:
                        raise ValueError(f"Hostname {host} resolves to private IP {ip_str}")
                except ipaddress.AddressValueError:
                    continue
        except socket.gaierror:
            raise ValueError(f"Cannot resolve hostname: {host}")
        except Exception as e:
            self.logger.warning(f"DNS resolution warning for {host}: {e}")
            # Continue but log the warning

    def _create_error_result(self, url: str, error_message: str) -> Dict[str, Any]:
        """Create standardized error result structure."""
        return {
            "url": url,
            "success": False,
            "error": error_message,
            "timestamp": (
                asyncio.get_event_loop().time() if asyncio.get_event_loop().is_running() else 0
            ),
            "content": {"text": "", "markdown": "", "word_count": 0},
            "metadata": {"status_code": 0, "crawl_duration": 0},
            "extraction": {},
        }

    async def _async_crawl(
        self,
        url: str,
        keywords: List[str],
        extract_links: bool,
        extract_images: bool,
        max_depth: int,
    ) -> Dict[str, Any]:
        """
        Async crawling implementation with comprehensive configuration.

        Args:
            url: Target URL to crawl
            keywords: Focus keywords for extraction
            extract_links: Whether to extract links
            extract_images: Whether to extract images
            max_depth: Maximum crawl depth

        Returns:
            Structured crawl results dictionary
        """
        start_time = asyncio.get_event_loop().time()

        # Configure crawler with timeout and user agent
        crawler_config = {"verbose": False, "headless": True, "user_agent": self._user_agent}

        async with AsyncWebCrawler(**crawler_config) as crawler:
            # Configure crawling parameters
            crawl_config = {
                "word_count_threshold": 50,
                "extraction_strategy": (
                    "LLMExtractionStrategy" if keywords else "NoExtractionStrategy"
                ),
                "chunking_strategy": "RegexChunking",
                "bypass_cache": True,
                "process_iframes": False,  # Security consideration
                "remove_overlay_elements": True,
                "simulate_user": True,
                "override_navigator": True,
            }

            # Add timeout handling
            try:
                result = await asyncio.wait_for(
                    crawler.arun(url=url, **crawl_config), timeout=self._timeout
                )
            except asyncio.TimeoutError:
                raise asyncio.TimeoutError(f"Crawl timed out after {self._timeout} seconds")

            # Calculate crawl duration
            end_time = asyncio.get_event_loop().time()
            crawl_duration = end_time - start_time

            # Process and structure the results
            structured_result = self._process_crawl_result(
                result, keywords, extract_links, extract_images, crawl_duration
            )

            return structured_result

    def _process_crawl_result(
        self,
        result: Any,
        keywords: List[str],
        extract_links: bool,
        extract_images: bool,
        crawl_duration: float = 0.0,
    ) -> Dict[str, Any]:
        """
        Process and structure crawl results with comprehensive analysis.

        Args:
            result: Raw crawl result from Crawl4AI
            keywords: Focus keywords for analysis
            extract_links: Whether to process links
            extract_images: Whether to process images
            crawl_duration: Time taken for crawling

        Returns:
            Structured and analyzed crawl results
        """
        # Extract basic content safely
        raw_text = getattr(result, "cleaned_html", "") or getattr(result, "text", "")
        markdown_content = getattr(result, "markdown", "")

        structured = {
            "url": getattr(result, "url", ""),
            "title": self._clean_text(getattr(result, "title", "")),
            "success": getattr(result, "success", False),
            "content": {
                "text": self._clean_text(raw_text),
                "markdown": markdown_content,
                "word_count": len(raw_text.split()) if raw_text else 0,
                "char_count": len(raw_text) if raw_text else 0,
                "language": self._detect_language(raw_text),
            },
            "metadata": {
                "status_code": getattr(result, "status_code", 0),
                "response_headers": getattr(result, "response_headers", {}),
                "crawl_timestamp": getattr(result, "timestamp", ""),
                "crawl_duration": round(crawl_duration, 2),
                "content_type": self._extract_content_type(result),
                "page_size": len(raw_text) if raw_text else 0,
            },
            "extraction": {},
            "analysis": {},
        }

        # Extract and categorize links if requested
        if extract_links:
            structured["links"] = self._process_links(result, structured["url"])

        # Extract and analyze images if requested
        if extract_images:
            structured["images"] = self._process_images(result)

        # Perform keyword-focused extraction and analysis
        if keywords and raw_text:
            structured["extraction"]["keyword_analysis"] = self._analyze_keywords(
                raw_text, keywords
            )

        # Extract structured data if available
        if hasattr(result, "extracted_content") and result.extracted_content:
            structured["extraction"]["structured_data"] = result.extracted_content

        # Perform content analysis
        if raw_text:
            structured["analysis"] = self._analyze_content(raw_text)

        # Add quality metrics
        structured["quality_metrics"] = self._calculate_quality_metrics(structured)

        return structured

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""

        # Remove excessive whitespace and normalize
        cleaned = re.sub(r"\s+", " ", text.strip())

        # Remove control characters
        cleaned = re.sub(r"[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]", "", cleaned)

        return cleaned

    def _detect_language(self, text: str) -> str:
        """Simple language detection based on common words."""
        if not text:
            return "unknown"

        text_lower = text.lower()

        # Simple heuristic-based language detection
        english_indicators = [
            "the",
            "and",
            "or",
            "but",
            "in",
            "on",
            "at",
            "to",
            "for",
            "of",
            "with",
            "by",
        ]
        english_count = sum(1 for word in english_indicators if word in text_lower)

        if english_count >= 3:
            return "en"

        return "unknown"

    def _extract_content_type(self, result: Any) -> str:
        """Extract content type from response headers."""
        headers = getattr(result, "response_headers", {})
        if isinstance(headers, dict):
            return headers.get("content-type", "unknown")
        return "unknown"

    def _process_links(self, result: Any, base_url: str) -> Dict[str, Any]:
        """Process and categorize extracted links."""
        links_data = {"internal": [], "external": [], "total_count": 0, "unique_domains": set()}

        if hasattr(result, "links"):
            # Process internal links
            internal_links = getattr(result.links, "internal", [])
            links_data["internal"] = [
                self._normalize_link(link, base_url) for link in internal_links
            ]

            # Process external links
            external_links = getattr(result.links, "external", [])
            for link in external_links:
                normalized = self._normalize_link(link, base_url)
                links_data["external"].append(normalized)

                # Track unique domains
                try:
                    domain = urlparse(normalized).netloc
                    if domain:
                        links_data["unique_domains"].add(domain)
                except:
                    pass

        links_data["total_count"] = len(links_data["internal"]) + len(links_data["external"])
        links_data["unique_domains"] = list(links_data["unique_domains"])

        return links_data

    def _normalize_link(self, link: str, base_url: str) -> str:
        """Normalize and validate a link."""
        if not link:
            return ""

        # Handle relative URLs
        if link.startswith("/"):
            return urljoin(base_url, link)
        elif not link.startswith(("http://", "https://")):
            return urljoin(base_url, link)

        return link

    def _process_images(self, result: Any) -> Dict[str, Any]:
        """Process and analyze extracted images."""
        images_data = {"images": [], "total_count": 0, "formats": set(), "total_size_estimate": 0}

        if hasattr(result, "media") and hasattr(result.media, "images"):
            images = getattr(result.media, "images", [])

            for img in images:
                if isinstance(img, dict):
                    processed_img = {
                        "url": img.get("src", ""),
                        "alt_text": img.get("alt", ""),
                        "title": img.get("title", ""),
                        "width": img.get("width", 0),
                        "height": img.get("height", 0),
                    }

                    # Extract format from URL
                    img_url = processed_img["url"]
                    if img_url:
                        try:
                            format_match = re.search(r"\.([a-zA-Z]{3,4})(?:\?|$)", img_url)
                            if format_match:
                                images_data["formats"].add(format_match.group(1).lower())
                        except:
                            pass

                    images_data["images"].append(processed_img)

        images_data["total_count"] = len(images_data["images"])
        images_data["formats"] = list(images_data["formats"])

        return images_data

    def _analyze_keywords(self, text: str, keywords: List[str]) -> Dict[str, Any]:
        """
        Analyze keyword presence, context, and relevance in the text.

        Args:
            text: Text content to analyze
            keywords: Keywords to search for and analyze

        Returns:
            Comprehensive keyword analysis results
        """
        text_lower = text.lower()
        sentences = re.split(r"[.!?]+", text)
        word_count = len(text.split())

        analysis = {
            "keyword_matches": {},
            "total_matches": 0,
            "keyword_density": {},
            "relevant_sentences": [],
            "context_analysis": {},
            "relevance_score": 0.0,
        }

        total_relevance = 0

        for keyword in keywords:
            keyword_lower = keyword.lower()

            # Count exact matches
            exact_matches = text_lower.count(keyword_lower)
            analysis["keyword_matches"][keyword] = exact_matches
            analysis["total_matches"] += exact_matches

            # Calculate keyword density
            if word_count > 0:
                density = (exact_matches / word_count) * 100
                analysis["keyword_density"][keyword] = round(density, 4)
                total_relevance += density

            # Extract relevant sentences with context
            relevant_sentences = []
            for sentence in sentences:
                sentence_clean = sentence.strip()
                if (
                    keyword_lower in sentence_clean.lower()
                    and len(sentence_clean) > 20
                    and len(relevant_sentences) < 3
                ):

                    # Add context score based on sentence length and keyword position
                    context_score = min(len(sentence_clean) / 100, 1.0)
                    relevant_sentences.append(
                        {"text": sentence_clean, "context_score": round(context_score, 2)}
                    )

            analysis["relevant_sentences"].extend(relevant_sentences)

            # Analyze keyword context
            analysis["context_analysis"][keyword] = self._analyze_keyword_context(
                text_lower, keyword_lower
            )

        # Calculate overall relevance score
        analysis["relevance_score"] = round(min(total_relevance / 10, 10.0), 2)

        return analysis

    def _analyze_keyword_context(self, text: str, keyword: str) -> Dict[str, Any]:
        """Analyze the context around keyword occurrences."""
        context_analysis = {"positions": [], "surrounding_words": [], "context_strength": 0.0}

        # Find all keyword positions
        start = 0
        while True:
            pos = text.find(keyword, start)
            if pos == -1:
                break

            context_analysis["positions"].append(pos)

            # Extract surrounding words (±5 words)
            words = text.split()
            word_pos = len(text[:pos].split())
            start_word = max(0, word_pos - 5)
            end_word = min(len(words), word_pos + 6)

            surrounding = words[start_word:end_word]
            context_analysis["surrounding_words"].extend(surrounding)

            start = pos + 1

        # Calculate context strength based on surrounding word diversity
        unique_words = set(context_analysis["surrounding_words"])
        if len(context_analysis["surrounding_words"]) > 0:
            context_analysis["context_strength"] = round(
                len(unique_words) / len(context_analysis["surrounding_words"]), 2
            )

        return context_analysis

    def _analyze_content(self, text: str) -> Dict[str, Any]:
        """
        Perform comprehensive content analysis.

        Args:
            text: Text content to analyze

        Returns:
            Content analysis results including readability, structure, etc.
        """
        if not text:
            return {}

        words = text.split()
        sentences = re.split(r"[.!?]+", text)
        paragraphs = text.split("\n\n")

        analysis = {
            "readability": self._calculate_readability(text, words, sentences),
            "structure": {
                "paragraph_count": len([p for p in paragraphs if p.strip()]),
                "sentence_count": len([s for s in sentences if s.strip()]),
                "avg_sentence_length": round(len(words) / max(len(sentences), 1), 1),
                "avg_word_length": round(sum(len(word) for word in words) / max(len(words), 1), 1),
            },
            "content_indicators": self._identify_content_indicators(text),
            "entities": self._extract_simple_entities(text),
            "sentiment_indicators": self._analyze_sentiment_indicators(text),
        }

        return analysis

    def _calculate_readability(
        self, text: str, words: List[str], sentences: List[str]
    ) -> Dict[str, Any]:
        """Calculate basic readability metrics."""
        if not words or not sentences:
            return {"flesch_score": 0, "reading_level": "unknown"}

        # Simple Flesch Reading Ease approximation
        avg_sentence_length = len(words) / len(sentences)
        avg_syllables = sum(self._count_syllables(word) for word in words) / len(words)

        flesch_score = 206.835 - (1.015 * avg_sentence_length) - (84.6 * avg_syllables)
        flesch_score = max(0, min(100, flesch_score))  # Clamp to 0-100

        # Determine reading level
        if flesch_score >= 90:
            reading_level = "very_easy"
        elif flesch_score >= 80:
            reading_level = "easy"
        elif flesch_score >= 70:
            reading_level = "fairly_easy"
        elif flesch_score >= 60:
            reading_level = "standard"
        elif flesch_score >= 50:
            reading_level = "fairly_difficult"
        elif flesch_score >= 30:
            reading_level = "difficult"
        else:
            reading_level = "very_difficult"

        return {
            "flesch_score": round(flesch_score, 1),
            "reading_level": reading_level,
            "avg_sentence_length": round(avg_sentence_length, 1),
            "avg_syllables_per_word": round(avg_syllables, 1),
        }

    def _count_syllables(self, word: str) -> int:
        """Simple syllable counting heuristic."""
        word = word.lower()
        vowels = "aeiouy"
        syllable_count = 0
        prev_was_vowel = False

        for char in word:
            is_vowel = char in vowels
            if is_vowel and not prev_was_vowel:
                syllable_count += 1
            prev_was_vowel = is_vowel

        # Handle silent 'e'
        if word.endswith("e") and syllable_count > 1:
            syllable_count -= 1

        return max(1, syllable_count)  # Every word has at least 1 syllable

    def _identify_content_indicators(self, text: str) -> Dict[str, Any]:
        """Identify various content type indicators."""
        text_lower = text.lower()

        indicators = {
            "news_indicators": any(
                word in text_lower
                for word in [
                    "breaking",
                    "news",
                    "report",
                    "journalist",
                    "correspondent",
                    "reuters",
                    "ap news",
                ]
            ),
            "academic_indicators": any(
                word in text_lower
                for word in [
                    "abstract",
                    "methodology",
                    "conclusion",
                    "references",
                    "doi:",
                    "journal",
                ]
            ),
            "commercial_indicators": any(
                word in text_lower
                for word in ["buy now", "purchase", "price", "discount", "sale", "order", "cart"]
            ),
            "social_media_indicators": any(
                word in text_lower
                for word in ["like", "share", "follow", "tweet", "post", "comment", "hashtag"]
            ),
            "government_indicators": any(
                word in text_lower
                for word in ["government", "official", "policy", "regulation", "law", "act", "bill"]
            ),
        }

        return indicators

    def _extract_simple_entities(self, text: str) -> List[str]:
        """Extract potential named entities using simple heuristics."""
        # Find capitalized words that might be entities
        entity_pattern = r"\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b"
        potential_entities = re.findall(entity_pattern, text)

        # Filter out common words and short entities
        common_words = {"The", "This", "That", "These", "Those", "And", "But", "Or", "So"}
        entities = [
            entity
            for entity in potential_entities
            if entity not in common_words and len(entity) > 2
        ]

        # Remove duplicates and limit results
        return list(set(entities))[:20]

    def _analyze_sentiment_indicators(self, text: str) -> Dict[str, Any]:
        """Analyze basic sentiment indicators in the text."""
        text_lower = text.lower()

        positive_words = [
            "good",
            "great",
            "excellent",
            "amazing",
            "wonderful",
            "fantastic",
            "positive",
            "success",
            "achievement",
            "victory",
            "progress",
        ]

        negative_words = [
            "bad",
            "terrible",
            "awful",
            "horrible",
            "disaster",
            "failure",
            "negative",
            "crisis",
            "problem",
            "issue",
            "concern",
            "worry",
        ]

        neutral_words = [
            "said",
            "stated",
            "reported",
            "according",
            "mentioned",
            "noted",
            "indicated",
            "suggested",
            "explained",
            "described",
        ]

        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        neutral_count = sum(1 for word in neutral_words if word in text_lower)

        total_sentiment_words = positive_count + negative_count + neutral_count

        if total_sentiment_words == 0:
            sentiment_score = 0
        else:
            sentiment_score = (positive_count - negative_count) / total_sentiment_words

        return {
            "positive_indicators": positive_count,
            "negative_indicators": negative_count,
            "neutral_indicators": neutral_count,
            "sentiment_score": round(sentiment_score, 2),
            "sentiment_classification": self._classify_sentiment(sentiment_score),
        }

    def _classify_sentiment(self, score: float) -> str:
        """Classify sentiment based on score."""
        if score > 0.1:
            return "positive"
        elif score < -0.1:
            return "negative"
        else:
            return "neutral"

    def _calculate_quality_metrics(self, structured_result: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall quality metrics for the crawled content."""
        content = structured_result.get("content", {})
        metadata = structured_result.get("metadata", {})

        word_count = content.get("word_count", 0)
        char_count = content.get("char_count", 0)
        status_code = metadata.get("status_code", 0)

        # Calculate quality score based on various factors
        quality_factors = {
            "content_length": min(word_count / 500, 1.0),  # Normalize to 500 words
            "successful_crawl": 1.0 if status_code == 200 else 0.0,
            "has_title": 1.0 if structured_result.get("title") else 0.0,
            "content_ratio": min(char_count / 1000, 1.0) if char_count > 0 else 0.0,
        }

        # Add keyword relevance if available
        extraction = structured_result.get("extraction", {})
        if "keyword_analysis" in extraction:
            relevance_score = extraction["keyword_analysis"].get("relevance_score", 0)
            quality_factors["keyword_relevance"] = min(relevance_score / 5.0, 1.0)

        # Calculate overall quality score
        overall_quality = sum(quality_factors.values()) / len(quality_factors)

        return {
            "overall_score": round(overall_quality, 2),
            "factors": {k: round(v, 2) for k, v in quality_factors.items()},
            "classification": self._classify_quality(overall_quality),
        }

    def _classify_quality(self, score: float) -> str:
        """Classify content quality based on score."""
        if score >= 0.8:
            return "high"
        elif score >= 0.6:
            return "medium"
        elif score >= 0.4:
            return "low"
        else:
            return "very_low"


class FocusedNewsCrawler(Crawl4AITool):
    """
    Specialized crawler for news websites with enhanced news analysis.

    This crawler is optimized for news article extraction with specialized
    analysis for journalistic content, credibility assessment, and news-specific
    entity extraction.

    Features:
        - News article type classification
        - Credibility indicator assessment
        - Publication metadata extraction
        - Enhanced entity recognition for news content
        - Source attribution analysis
    """

    name: str = "focused_news_crawler"
    description: str = (
        """Crawl news websites and extract article content with specialized news analysis. Optimized for news article structure, credibility assessment, and journalistic content extraction."""
    )

    def __init__(self) -> None:
        """Initialize the news crawler with news-specific keywords."""
        super().__init__(
            focus_keywords=[
                "breaking",
                "news",
                "report",
                "analysis",
                "investigation",
                "sources",
                "officials",
                "statement",
                "confirmed",
                "according",
                "journalist",
                "correspondent",
                "exclusive",
                "developing",
            ]
        )

        self.logger.info("Focused news crawler initialized")

    def _process_crawl_result(
        self,
        result: Any,
        keywords: List[str],
        extract_links: bool,
        extract_images: bool,
        crawl_duration: float = 0.0,
    ) -> Dict[str, Any]:
        """
        Enhanced processing for news articles with specialized analysis.

        Args:
            result: Raw crawl result
            keywords: Focus keywords
            extract_links: Whether to extract links
            extract_images: Whether to extract images
            crawl_duration: Crawl duration

        Returns:
            Enhanced structured result with news-specific analysis
        """
        # Get base structured result
        structured = super()._process_crawl_result(
            result, keywords, extract_links, extract_images, crawl_duration
        )

        # Add news-specific analysis
        text = structured["content"]["text"]
        if text:
            structured["extraction"]["news_analysis"] = {
                "article_type": self._classify_article_type(text),
                "key_entities": self._extract_news_entities(text),
                "publication_indicators": self._find_publication_info(text),
                "credibility_indicators": self._assess_credibility(text),
                "source_analysis": self._analyze_sources(text),
                "temporal_indicators": self._extract_temporal_info(text),
            }

        return structured

    def _classify_article_type(self, text: str) -> Dict[str, Any]:
        """
        Classify the type of news article with confidence scoring.

        Args:
            text: Article text to classify

        Returns:
            Classification result with type and confidence
        """
        text_lower = text.lower()

        # Define classification patterns with weights
        patterns = {
            "breaking_news": {
                "keywords": ["breaking", "urgent", "alert", "just in", "developing"],
                "weight": 1.0,
            },
            "analysis": {
                "keywords": ["analysis", "opinion", "editorial", "perspective", "commentary"],
                "weight": 0.8,
            },
            "investigative": {
                "keywords": ["investigation", "exclusive", "revealed", "uncovered", "expose"],
                "weight": 0.9,
            },
            "sports": {
                "keywords": ["game", "match", "score", "team", "player", "championship"],
                "weight": 0.7,
            },
            "business": {
                "keywords": ["market", "stock", "economy", "financial", "earnings", "company"],
                "weight": 0.7,
            },
            "politics": {
                "keywords": [
                    "government",
                    "election",
                    "policy",
                    "politician",
                    "congress",
                    "senate",
                ],
                "weight": 0.7,
            },
        }

        scores = {}
        for article_type, pattern in patterns.items():
            score = 0
            for keyword in pattern["keywords"]:
                if keyword in text_lower:
                    score += pattern["weight"]
            scores[article_type] = score

        # Determine primary type
        if max(scores.values()) == 0:
            primary_type = "general_news"
            confidence = 0.5
        else:
            primary_type = max(scores, key=scores.get)
            confidence = min(scores[primary_type] / 3.0, 1.0)  # Normalize confidence

        return {
            "primary_type": primary_type,
            "confidence": round(confidence, 2),
            "all_scores": {k: round(v, 2) for k, v in scores.items()},
        }

    def _extract_news_entities(self, text: str) -> Dict[str, List[str]]:
        """
        Extract news-specific entities with categorization.

        Args:
            text: Text to extract entities from

        Returns:
            Categorized entities dictionary
        """
        entities = {"people": [], "organizations": [], "locations": [], "other": []}

        # Enhanced entity patterns for news
        person_indicators = [
            "said",
            "told",
            "according to",
            "spokesperson",
            "minister",
            "president",
            "ceo",
        ]
        org_indicators = [
            "company",
            "corporation",
            "inc",
            "ltd",
            "organization",
            "agency",
            "department",
        ]
        location_indicators = ["city", "country", "state", "province", "region", "county"]

        # Find capitalized sequences (potential entities)
        entity_pattern = r"\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b"
        potential_entities = re.findall(entity_pattern, text)

        # Categorize entities based on context
        for entity in set(potential_entities):
            if len(entity) <= 2:
                continue

            entity_context = self._get_entity_context(text, entity)

            if any(indicator in entity_context.lower() for indicator in person_indicators):
                entities["people"].append(entity)
            elif any(indicator in entity_context.lower() for indicator in org_indicators):
                entities["organizations"].append(entity)
            elif any(indicator in entity_context.lower() for indicator in location_indicators):
                entities["locations"].append(entity)
            else:
                entities["other"].append(entity)

        # Limit results and remove common false positives
        common_false_positives = {
            "The",
            "This",
            "That",
            "These",
            "Those",
            "And",
            "But",
            "Or",
            "So",
            "News",
            "Report",
        }

        for category in entities:
            entities[category] = [
                entity for entity in entities[category] if entity not in common_false_positives
            ][
                :10
            ]  # Limit to 10 per category

        return entities

    def _get_entity_context(self, text: str, entity: str) -> str:
        """Get surrounding context for an entity."""
        # Find entity position and extract surrounding words
        entity_pos = text.find(entity)
        if entity_pos == -1:
            return ""

        start = max(0, entity_pos - 100)
        end = min(len(text), entity_pos + len(entity) + 100)

        return text[start:end]

    def _find_publication_info(self, text: str) -> Dict[str, Any]:
        """
        Find publication metadata and journalistic indicators.

        Args:
            text: Article text to analyze

        Returns:
            Publication information and metadata
        """
        text_lower = text.lower()

        # Date patterns
        date_patterns = [
            r"\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b",  # MM/DD/YYYY or MM-DD-YYYY
            r"\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b",  # YYYY/MM/DD or YYYY-MM-DD
            r"\b(?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4}\b",
        ]

        has_date = any(re.search(pattern, text_lower) for pattern in date_patterns)

        # Byline patterns
        byline_patterns = [
            r"\bby\s+[A-Z][a-z]+\s+[A-Z][a-z]+\b",  # "By First Last"
            r"\bauthor:\s*[A-Z][a-z]+",  # "Author: Name"
            r"\breporter:\s*[A-Z][a-z]+",  # "Reporter: Name"
        ]

        has_byline = any(re.search(pattern, text) for pattern in byline_patterns)

        # Source attribution patterns
        attribution_patterns = [
            "according to",
            "sources say",
            "reported by",
            "confirmed by",
            "officials said",
            "spokesperson said",
            "statement said",
            "sources told",
            "informed sources",
            "reliable sources",
        ]

        attribution_count = sum(1 for phrase in attribution_patterns if phrase in text_lower)

        return {
            "has_byline": has_byline,
            "has_dateline": has_date,
            "has_source_attribution": attribution_count > 0,
            "attribution_count": attribution_count,
            "publication_indicators": {
                "has_contact_info": any(
                    pattern in text_lower for pattern in ["email", "@", "contact"]
                ),
                "has_correction_policy": "correction" in text_lower,
                "has_editor_note": "editor" in text_lower,
            },
        }

    def _assess_credibility(self, text: str) -> Dict[str, Any]:
        """
        Assess credibility indicators in the article.

        Args:
            text: Article text to analyze

        Returns:
            Credibility assessment with scoring
        """
        text_lower = text.lower()

        # Positive credibility indicators
        positive_indicators = {
            "has_quotes": '"' in text or "'" in text,
            "has_official_sources": any(
                phrase in text_lower
                for phrase in [
                    "official",
                    "spokesperson",
                    "government",
                    "ministry",
                    "department",
                    "press release",
                    "official statement",
                    "confirmed by officials",
                ]
            ),
            "has_verification": any(
                phrase in text_lower
                for phrase in [
                    "verified",
                    "confirmed",
                    "authenticated",
                    "fact-checked",
                    "independently verified",
                    "cross-referenced",
                ]
            ),
            "has_multiple_sources": text_lower.count("source") > 1,
            "has_expert_quotes": any(
                phrase in text_lower
                for phrase in ["expert", "professor", "researcher", "analyst", "specialist"]
            ),
        }

        # Negative credibility indicators
        negative_indicators = {
            "speculation_indicators": any(
                phrase in text_lower
                for phrase in [
                    "allegedly",
                    "reportedly",
                    "rumored",
                    "unconfirmed",
                    "speculation",
                    "claims",
                    "suggests",
                    "appears to",
                    "seems to",
                ]
            ),
            "sensational_language": any(
                phrase in text_lower
                for phrase in ["shocking", "incredible", "unbelievable", "amazing", "stunning"]
            ),
            "anonymous_sources_only": "anonymous" in text_lower and "source" in text_lower,
            "opinion_markers": any(
                phrase in text_lower
                for phrase in ["i think", "i believe", "in my opinion", "personally"]
            ),
        }

        # Calculate credibility score
        positive_score = sum(1 for indicator in positive_indicators.values() if indicator)
        negative_score = sum(1 for indicator in negative_indicators.values() if indicator)

        total_possible = len(positive_indicators)
        credibility_score = max(0, (positive_score - negative_score) / total_possible)

        return {
            "positive_indicators": positive_indicators,
            "negative_indicators": negative_indicators,
            "credibility_score": round(credibility_score, 2),
            "credibility_level": self._classify_credibility(credibility_score),
        }

    def _classify_credibility(self, score: float) -> str:
        """Classify credibility level based on score."""
        if score >= 0.7:
            return "high"
        elif score >= 0.4:
            return "medium"
        elif score >= 0.2:
            return "low"
        else:
            return "very_low"

    def _analyze_sources(self, text: str) -> Dict[str, Any]:
        """Analyze source types and attribution in the article."""
        text_lower = text.lower()

        source_types = {
            "government": ["government", "official", "ministry", "department", "agency"],
            "academic": ["university", "professor", "researcher", "study", "research"],
            "corporate": ["company", "corporation", "ceo", "executive", "spokesperson"],
            "media": ["news", "reporter", "journalist", "correspondent", "media"],
            "anonymous": ["anonymous", "unnamed", "sources say", "officials say"],
        }

        source_analysis = {}
        for source_type, keywords in source_types.items():
            count = sum(1 for keyword in keywords if keyword in text_lower)
            source_analysis[source_type] = count

        total_sources = sum(source_analysis.values())

        return {
            "source_breakdown": source_analysis,
            "total_source_mentions": total_sources,
            "source_diversity": len([k for k, v in source_analysis.items() if v > 0]),
            "primary_source_type": (
                max(source_analysis, key=source_analysis.get) if total_sources > 0 else "none"
            ),
        }

    def _extract_temporal_info(self, text: str) -> Dict[str, Any]:
        """Extract temporal information and timeline indicators."""
        text_lower = text.lower()

        # Time indicators
        time_indicators = {
            "immediate": ["now", "currently", "today", "this morning", "this afternoon"],
            "recent": ["yesterday", "last week", "recently", "lately", "this week"],
            "historical": ["last year", "years ago", "decades ago", "historically"],
            "future": ["will", "plans to", "expected to", "scheduled", "upcoming"],
        }

        temporal_analysis = {}
        for time_type, indicators in time_indicators.items():
            count = sum(1 for indicator in indicators if indicator in text_lower)
            temporal_analysis[time_type] = count

        # Detect timeline structure
        timeline_markers = ["first", "then", "next", "finally", "meanwhile", "subsequently"]
        has_timeline = sum(1 for marker in timeline_markers if marker in text_lower) >= 2

        return {
            "temporal_breakdown": temporal_analysis,
            "has_timeline_structure": has_timeline,
            "temporal_focus": (
                max(temporal_analysis, key=temporal_analysis.get)
                if any(temporal_analysis.values())
                else "unclear"
            ),
        }
