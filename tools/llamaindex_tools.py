"""
LlamaIndex integration tools for RAG capabilities.
"""

import json
from typing import Any, Dict, List, Optional

from crewai.tools import BaseTool
from llama_index.core import Document, QueryBundle, VectorStoreIndex
from llama_index.core.query_engine import RetrieverQ<PERSON>yEngine
from llama_index.core.response_synthesizers import ResponseMode
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from pydantic import BaseModel, Field


class RAGQueryInput(BaseModel):
    """Input schema for RAG query tool."""

    query: str = Field(description="Query to search in the knowledge base")
    top_k: int = Field(default=5, description="Number of top results to retrieve")
    similarity_threshold: float = Field(default=0.7, description="Minimum similarity threshold")


class LlamaIndexRAGTool(BaseTool):
    """
    CrewAI tool wrapper for LlamaIndex RAG capabilities.

    Provides retrieval-augmented generation using a vector store index
    for answering questions based on indexed documents.
    """

    name: str = "llamaindex_rag_search"
    description: str = (
        """Search through indexed documents using semantic similarity. Useful for finding relevant information from a knowledge base of previously crawled and indexed documents. Returns contextual answers based on the most relevant document chunks."""
    )
    args_schema: type[BaseModel] = RAGQueryInput

    def __init__(self):
        """Initialize the LlamaIndex RAG tool."""
        super().__init__()
        self._index = None
        self._query_engine = None

        # Initialize LLM and embedding model
        self._llm = OpenAI(model="gpt-3.5-turbo", temperature=0.1)
        self._embed_model = OpenAIEmbedding()

    def set_index(self, index: VectorStoreIndex):
        """
        Set the vector store index for RAG queries.

        Args:
            index: LlamaIndex VectorStoreIndex instance
        """
        self._index = index

        # Create query engine with custom configuration
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=10,
        )

        self._query_engine = RetrieverQueryEngine.from_args(
            retriever=retriever, llm=self._llm, response_mode=ResponseMode.COMPACT, verbose=True
        )

    def _run(self, query: str, top_k: int = 5, similarity_threshold: float = 0.7) -> str:
        """
        Execute a RAG query against the indexed documents.

        Args:
            query: Query string to search for
            top_k: Number of top results to retrieve
            similarity_threshold: Minimum similarity threshold

        Returns:
            JSON string with query results and sources
        """
        if not self._index or not self._query_engine:
            return json.dumps(
                {
                    "error": "No index available. Please build a knowledge base first.",
                    "query": query,
                    "results": [],
                }
            )

        try:
            # Execute the query
            response = self._query_engine.query(query)

            # Extract source information
            sources = []
            if hasattr(response, "source_nodes"):
                for node in response.source_nodes:
                    source_info = {
                        "content": (
                            node.node.text[:500] + "..."
                            if len(node.node.text) > 500
                            else node.node.text
                        ),
                        "score": float(node.score) if hasattr(node, "score") else 0.0,
                        "metadata": node.node.metadata if hasattr(node.node, "metadata") else {},
                    }

                    # Only include sources above similarity threshold
                    if source_info["score"] >= similarity_threshold:
                        sources.append(source_info)

            # Limit to top_k results
            sources = sources[:top_k]

            result = {
                "query": query,
                "answer": str(response),
                "sources": sources,
                "total_sources": len(sources),
                "confidence": self._calculate_confidence(sources),
                "metadata": {"similarity_threshold": similarity_threshold, "top_k": top_k},
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            return json.dumps(
                {"error": f"Error executing RAG query: {str(e)}", "query": query, "results": []}
            )

    def _calculate_confidence(self, sources: List[Dict[str, Any]]) -> str:
        """Calculate confidence level based on source scores."""
        if not sources:
            return "Low"

        avg_score = sum(source["score"] for source in sources) / len(sources)

        if avg_score >= 0.8:
            return "High"
        elif avg_score >= 0.6:
            return "Medium"
        else:
            return "Low"


class DocumentSummaryTool(BaseTool):
    """Tool for generating summaries of indexed documents."""

    name: str = "document_summary"
    description: str = (
        """Generate summaries of documents in the knowledge base. Useful for getting overviews of large document collections or specific document topics."""
    )

    def __init__(self, index=None):
        """Initialize the document summary tool."""
        super().__init__()
        self._index = index
        self._llm = OpenAI(model="gpt-3.5-turbo", temperature=0.1)

    def set_index(self, index: VectorStoreIndex):
        """Set the vector store index."""
        self._index = index

    def _run(self, topic: str = "", max_docs: int = 10) -> str:
        """
        Generate a summary of documents related to a topic.

        Args:
            topic: Topic to focus the summary on (empty for general summary)
            max_docs: Maximum number of documents to include

        Returns:
            JSON string with document summary
        """
        if not self._index:
            return json.dumps({"error": "No index available", "summary": ""})

        try:
            # Get document nodes from the index
            retriever = VectorIndexRetriever(index=self._index, similarity_top_k=max_docs)

            if topic:
                # Retrieve documents related to the topic
                query_bundle = QueryBundle(query_str=topic)
                nodes = retriever.retrieve(query_bundle)
            else:
                # Get a sample of all documents
                # This is a simplified approach - in practice, you'd want better sampling
                query_bundle = QueryBundle(query_str="summary overview")
                nodes = retriever.retrieve(query_bundle)

            # Extract document content
            doc_contents = []
            for node in nodes[:max_docs]:
                content = node.node.text[:1000]  # Limit content length
                doc_contents.append(content)

            # Generate summary using LLM
            combined_content = "\n\n".join(doc_contents)
            summary_prompt = f"""
            Please provide a comprehensive summary of the following documents:

            {combined_content}

            Focus on:
            1. Main themes and topics
            2. Key findings or insights
            3. Important facts and figures
            4. Overall conclusions

            Summary:
            """

            summary_response = self._llm.complete(summary_prompt)

            result = {
                "topic": topic or "General",
                "summary": str(summary_response),
                "documents_analyzed": len(doc_contents),
                "total_characters": len(combined_content),
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            return json.dumps({"error": f"Error generating summary: {str(e)}", "summary": ""})


class SemanticSearchTool(BaseTool):
    """Tool for semantic search across indexed documents."""

    name: str = "semantic_search"
    description: str = (
        """Perform semantic search to find conceptually similar content even when exact keywords don't match. Useful for finding related information and discovering connections."""
    )

    def __init__(self, index=None):
        """Initialize the semantic search tool."""
        super().__init__()
        self._index = index

    def set_index(self, index: VectorStoreIndex):
        """Set the vector store index."""
        self._index = index

    def _run(self, concept: str, top_k: int = 10, include_metadata: bool = True) -> str:
        """
        Perform semantic search for a concept.

        Args:
            concept: Concept or idea to search for
            top_k: Number of results to return
            include_metadata: Whether to include document metadata

        Returns:
            JSON string with search results
        """
        if not self._index:
            return json.dumps({"error": "No index available", "results": []})

        try:
            # Create retriever
            retriever = VectorIndexRetriever(index=self._index, similarity_top_k=top_k)

            # Perform semantic search
            query_bundle = QueryBundle(query_str=concept)
            nodes = retriever.retrieve(query_bundle)

            # Format results
            results = []
            for i, node in enumerate(nodes):
                result = {
                    "rank": i + 1,
                    "content": node.node.text,
                    "similarity_score": float(node.score) if hasattr(node, "score") else 0.0,
                    "content_preview": (
                        node.node.text[:200] + "..."
                        if len(node.node.text) > 200
                        else node.node.text
                    ),
                }

                if include_metadata and hasattr(node.node, "metadata"):
                    result["metadata"] = node.node.metadata

                results.append(result)

            response = {"concept": concept, "total_results": len(results), "results": results}

            return json.dumps(response, indent=2)

        except Exception as e:
            return json.dumps(
                {"error": f"Error performing semantic search: {str(e)}", "results": []}
            )
