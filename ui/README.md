# 🧠 OSINT Framework - Streamlit UI

Interactive web interface for the CrewAI OSINT Agent Framework.

## 🚀 Quick Start

### Launch the UI
```bash
# From project root
python run_ui.py

# Or directly with streamlit
streamlit run ui/streamlit_app.py
```

The UI will be available at: http://localhost:8501

## 🎯 Features

### 🏠 Home Dashboard
- Framework overview
- Quick access to analysis types
- Recent analysis history

### 🌍 Geopolitical Analysis
- **Intelligence Brief Generation**: Create structured geopolitical reports
- **Regional Monitoring**: Set up continuous monitoring for specific regions
- **Situation Reports**: Multi-region analysis and assessment
- **Custom Analysis**: Flexible analysis with custom queries

### 🔒 Cyber Threat Intelligence
- **IOC Extraction**: Extract indicators from threat reports
- **Threat Actor Tracking**: Research and profile threat actors
- **Campaign Analysis**: Correlate and analyze threat campaigns
- **IOC Report Generation**: Create structured IOC reports

### 📚 Document Management
- **Document Upload**: Index files, URLs, and text content
- **Advanced Search**: Semantic, keyword, and hybrid search
- **Index Management**: Monitor and manage document indexes

### 📊 Analytics Dashboard
- **Analysis Metrics**: Track usage and performance
- **Timeline Visualization**: View analysis trends over time
- **Activity Monitoring**: Recent analysis activity

### ⚙️ Settings & Configuration
- **API Key Management**: Configure OpenAI and Serper API keys
- **Framework Settings**: Adjust model parameters and search settings
- **Data Management**: Export/import analysis history

## 🔧 Configuration

### API Keys
The UI requires API keys to be configured in your `.env` file:

```bash
OPENAI_API_KEY=your_openai_api_key
SERPER_API_KEY=your_serper_api_key
```

### Environment Setup
Ensure you have the required dependencies installed:

```bash
pip install streamlit plotly altair
```

## 🎨 UI Components

### Navigation
- **Sidebar Navigation**: Easy access to all framework features
- **Quick Stats**: Real-time metrics in the sidebar
- **Page Routing**: Seamless navigation between features

### Interactive Elements
- **Form Inputs**: Dynamic forms for analysis configuration
- **File Uploads**: Drag-and-drop document upload
- **Real-time Updates**: Live status updates during processing
- **Download Options**: Export reports and data

### Visualizations
- **Charts and Graphs**: Analysis trends and distributions
- **Metrics Cards**: Key performance indicators
- **Progress Indicators**: Real-time processing status

## 🔍 Usage Examples

### Geopolitical Analysis
1. Navigate to "🌍 Geopolitical Analysis"
2. Select "Intelligence Brief"
3. Enter topic and select regions
4. Click "Generate Intelligence Brief"
5. View and download results

### CTI Analysis
1. Navigate to "🔒 Cyber Threat Intelligence"
2. Select "IOC Extraction"
3. Paste threat report text
4. Click "Extract IOCs"
5. Review extracted indicators

### Document Search
1. Navigate to "📚 Document Management"
2. Go to "Search Documents" tab
3. Enter search query
4. Select search type and filters
5. Review search results

## 🛠️ Customization

### Adding New Pages
1. Create new page function in `streamlit_app.py`
2. Add navigation option in `sidebar_navigation()`
3. Add routing logic in `main()`

### Styling
- Custom CSS is defined in the `st.markdown()` sections
- Modify the CSS classes to change appearance
- Add new styles for custom components

### Functionality
- Extend existing agent capabilities
- Add new analysis types
- Integrate additional data sources

## 🔒 Security Notes

- API keys are masked in the UI
- Sensitive data is not logged
- File uploads are processed in memory
- Analysis history is stored in session state

## 🐛 Troubleshooting

### Common Issues

**UI won't start:**
- Check if Streamlit is installed: `pip install streamlit`
- Verify Python path and dependencies

**API errors:**
- Verify API keys in `.env` file
- Check API key permissions and quotas

**Analysis failures:**
- Check internet connectivity
- Verify agent configurations
- Review error messages in UI

### Debug Mode
Run with debug logging:
```bash
streamlit run ui/streamlit_app.py --logger.level=debug
```

## 📈 Future Enhancements

- Real-time collaboration features
- Advanced visualization options
- Custom dashboard creation
- Integration with external tools
- Mobile-responsive design
- Multi-user authentication

---

**📅 Last Updated**: June 18, 2025  
**🔄 Status**: Fully functional with all core features implemented
