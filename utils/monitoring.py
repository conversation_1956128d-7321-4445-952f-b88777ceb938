"""
🧠 CrewAI OSINT Agent Framework - Monitoring and Metrics

Advanced monitoring system with health checks, performance metrics,
and alerting capabilities for production environments.
"""

import asyncio
import json
import os
import queue
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

import psutil

from .logging_config import osint_logger, performance_monitor


@dataclass
class SystemMetrics:
    """System performance metrics"""

    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_sent_mb: float
    network_recv_mb: float
    active_processes: int
    load_average: List[float]


@dataclass
class ApplicationMetrics:
    """Application-specific metrics"""

    timestamp: str
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    avg_response_time: float
    total_requests: int
    error_rate: float
    uptime_seconds: float
    api_keys_configured: bool
    vector_index_size: int


class HealthChecker:
    """Comprehensive health checking system"""

    def __init__(self):
        self.checks = {}
        self.last_check_time = None
        self.check_interval = 30  # seconds

    def register_check(self, name: str, check_func, critical: bool = False):
        """Register a health check function"""
        self.checks[name] = {
            "function": check_func,
            "critical": critical,
            "last_result": None,
            "last_check": None,
        }

    async def run_health_checks(self) -> Dict[str, Any]:
        """Run all registered health checks"""

        results = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "critical_failures": [],
        }

        for name, check_info in self.checks.items():
            try:
                start_time = time.time()

                if asyncio.iscoroutinefunction(check_info["function"]):
                    result = await check_info["function"]()
                else:
                    result = check_info["function"]()

                duration = time.time() - start_time

                check_result = {
                    "status": "healthy" if result else "unhealthy",
                    "duration": duration,
                    "details": result if isinstance(result, dict) else {"result": result},
                }

                results["checks"][name] = check_result

                # Track critical failures
                if not result and check_info["critical"]:
                    results["critical_failures"].append(name)
                    results["status"] = "unhealthy"

                # Update check info
                check_info["last_result"] = result
                check_info["last_check"] = datetime.now()

            except Exception as e:
                osint_logger.log_error(e, "health_check", {"check_name": name})

                check_result = {"status": "error", "error": str(e), "duration": 0}

                results["checks"][name] = check_result

                if check_info["critical"]:
                    results["critical_failures"].append(name)
                    results["status"] = "unhealthy"

        self.last_check_time = datetime.now()
        return results


class MetricsCollector:
    """Collect and store system and application metrics"""

    def __init__(self, collection_interval: int = 60):
        self.collection_interval = collection_interval
        self.metrics_history = []
        self.max_history_size = 1440  # 24 hours of minute-by-minute data
        self.running = False
        self.collection_thread = None

    def start_collection(self):
        """Start metrics collection in background thread"""
        if not self.running:
            self.running = True
            self.collection_thread = threading.Thread(target=self._collection_loop)
            self.collection_thread.daemon = True
            self.collection_thread.start()
            osint_logger.logger.info("Metrics collection started")

    def stop_collection(self):
        """Stop metrics collection"""
        self.running = False
        if self.collection_thread:
            self.collection_thread.join()
        osint_logger.logger.info("Metrics collection stopped")

    def _collection_loop(self):
        """Main collection loop"""
        while self.running:
            try:
                metrics = self.collect_metrics()
                self.store_metrics(metrics)
                time.sleep(self.collection_interval)
            except Exception as e:
                osint_logger.log_error(e, "metrics_collector")
                time.sleep(self.collection_interval)

    def collect_system_metrics(self) -> SystemMetrics:
        """Collect system performance metrics"""

        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)

        # Memory metrics
        memory = psutil.virtual_memory()
        memory_used_gb = memory.used / (1024**3)
        memory_total_gb = memory.total / (1024**3)

        # Disk metrics
        disk = psutil.disk_usage("/")
        disk_used_gb = disk.used / (1024**3)
        disk_total_gb = disk.total / (1024**3)

        # Network metrics
        network = psutil.net_io_counters()
        network_sent_mb = network.bytes_sent / (1024**2)
        network_recv_mb = network.bytes_recv / (1024**2)

        # Process metrics
        active_processes = len(psutil.pids())

        # Load average (Unix-like systems)
        try:
            load_average = list(os.getloadavg())
        except (OSError, AttributeError):
            load_average = [0.0, 0.0, 0.0]

        return SystemMetrics(
            timestamp=datetime.now().isoformat(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_gb=memory_used_gb,
            memory_total_gb=memory_total_gb,
            disk_percent=disk.percent,
            disk_used_gb=disk_used_gb,
            disk_total_gb=disk_total_gb,
            network_sent_mb=network_sent_mb,
            network_recv_mb=network_recv_mb,
            active_processes=active_processes,
            load_average=load_average,
        )

    def collect_application_metrics(self) -> ApplicationMetrics:
        """Collect application-specific metrics"""

        # Get performance stats
        perf_stats = performance_monitor.get_performance_stats()

        # Check API keys
        api_keys_configured = bool(os.getenv("OPENAI_API_KEY") and os.getenv("SERPER_API_KEY"))

        # Check vector index size
        vector_index_path = Path("data/vector_index")
        vector_index_size = 0
        if vector_index_path.exists():
            vector_index_size = sum(
                f.stat().st_size for f in vector_index_path.rglob("*") if f.is_file()
            )

        return ApplicationMetrics(
            timestamp=datetime.now().isoformat(),
            active_tasks=0,  # Would need to integrate with task manager
            completed_tasks=0,  # Would need to integrate with task manager
            failed_tasks=0,  # Would need to integrate with task manager
            avg_response_time=perf_stats.get("avg_response_time", 0),
            total_requests=perf_stats.get("total_requests", 0),
            error_rate=0.0,  # Would need to calculate from error logs
            uptime_seconds=perf_stats.get("uptime_seconds", 0),
            api_keys_configured=api_keys_configured,
            vector_index_size=vector_index_size,
        )

    def collect_metrics(self) -> Dict[str, Any]:
        """Collect all metrics"""

        system_metrics = self.collect_system_metrics()
        app_metrics = self.collect_application_metrics()

        return {"system": asdict(system_metrics), "application": asdict(app_metrics)}

    def store_metrics(self, metrics: Dict[str, Any]):
        """Store metrics in memory and optionally persist"""

        self.metrics_history.append(metrics)

        # Limit history size
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size :]

        # Optionally persist to file
        self._persist_metrics(metrics)

    def _persist_metrics(self, metrics: Dict[str, Any]):
        """Persist metrics to file"""

        try:
            metrics_dir = Path("logs/metrics")
            metrics_dir.mkdir(parents=True, exist_ok=True)

            # Daily metrics file
            date_str = datetime.now().strftime("%Y-%m-%d")
            metrics_file = metrics_dir / f"metrics_{date_str}.jsonl"

            with open(metrics_file, "a") as f:
                f.write(json.dumps(metrics) + "\n")

        except Exception as e:
            osint_logger.log_error(e, "metrics_persistence")

    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """Get metrics summary for the last N hours"""

        cutoff_time = datetime.now() - timedelta(hours=hours)

        recent_metrics = [
            m
            for m in self.metrics_history
            if datetime.fromisoformat(m["system"]["timestamp"]) > cutoff_time
        ]

        if not recent_metrics:
            return {"status": "no_data", "hours": hours}

        # Calculate averages
        cpu_values = [m["system"]["cpu_percent"] for m in recent_metrics]
        memory_values = [m["system"]["memory_percent"] for m in recent_metrics]
        response_times = [m["application"]["avg_response_time"] for m in recent_metrics]

        return {
            "period_hours": hours,
            "data_points": len(recent_metrics),
            "avg_cpu_percent": sum(cpu_values) / len(cpu_values),
            "max_cpu_percent": max(cpu_values),
            "avg_memory_percent": sum(memory_values) / len(memory_values),
            "max_memory_percent": max(memory_values),
            "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
            "max_response_time": max(response_times) if response_times else 0,
            "latest_metrics": recent_metrics[-1] if recent_metrics else None,
        }


class AlertManager:
    """Manage alerts and notifications"""

    def __init__(self):
        self.alert_rules = []
        self.active_alerts = {}
        self.alert_history = []

    def add_alert_rule(
        self, name: str, condition_func, severity: str = "warning", cooldown_minutes: int = 5
    ):
        """Add an alert rule"""

        self.alert_rules.append(
            {
                "name": name,
                "condition": condition_func,
                "severity": severity,
                "cooldown_minutes": cooldown_minutes,
                "last_triggered": None,
            }
        )

    def check_alerts(self, metrics: Dict[str, Any]):
        """Check all alert rules against current metrics"""

        current_time = datetime.now()

        for rule in self.alert_rules:
            try:
                # Check if rule is in cooldown
                if rule["last_triggered"] and current_time - rule["last_triggered"] < timedelta(
                    minutes=rule["cooldown_minutes"]
                ):
                    continue

                # Evaluate condition
                if rule["condition"](metrics):
                    self._trigger_alert(rule, metrics)

            except Exception as e:
                osint_logger.log_error(e, "alert_manager", {"rule_name": rule["name"]})

    def _trigger_alert(self, rule: Dict[str, Any], metrics: Dict[str, Any]):
        """Trigger an alert"""

        alert = {
            "name": rule["name"],
            "severity": rule["severity"],
            "timestamp": datetime.now().isoformat(),
            "metrics": metrics,
            "message": f"Alert triggered: {rule['name']}",
        }

        # Store alert
        self.active_alerts[rule["name"]] = alert
        self.alert_history.append(alert)

        # Update rule
        rule["last_triggered"] = datetime.now()

        # Log alert
        osint_logger.logger.warning(
            "Alert triggered", alert_name=rule["name"], severity=rule["severity"], metrics=metrics
        )

        # Send notification (implement as needed)
        self._send_notification(alert)

    def _send_notification(self, alert: Dict[str, Any]):
        """Send alert notification (implement based on requirements)"""

        # This could integrate with:
        # - Email notifications
        # - Slack/Discord webhooks
        # - PagerDuty
        # - SMS services

        osint_logger.logger.info(
            "Alert notification sent", alert_name=alert["name"], severity=alert["severity"]
        )


# Global instances
health_checker = HealthChecker()
metrics_collector = MetricsCollector()
alert_manager = AlertManager()


# Default health checks
def check_api_keys():
    """Check if required API keys are configured"""
    return bool(os.getenv("OPENAI_API_KEY") and os.getenv("SERPER_API_KEY"))


def check_disk_space():
    """Check available disk space"""
    disk = psutil.disk_usage("/")
    return disk.percent < 90  # Alert if disk usage > 90%


def check_memory_usage():
    """Check memory usage"""
    memory = psutil.virtual_memory()
    return memory.percent < 85  # Alert if memory usage > 85%


# Register default health checks
health_checker.register_check("api_keys", check_api_keys, critical=True)
health_checker.register_check("disk_space", check_disk_space, critical=True)
health_checker.register_check("memory_usage", check_memory_usage, critical=False)


# Default alert rules
def high_cpu_alert(metrics):
    """Alert on high CPU usage"""
    return metrics["system"]["cpu_percent"] > 80


def high_memory_alert(metrics):
    """Alert on high memory usage"""
    return metrics["system"]["memory_percent"] > 85


def slow_response_alert(metrics):
    """Alert on slow response times"""
    return metrics["application"]["avg_response_time"] > 5.0


# Register default alerts
alert_manager.add_alert_rule("high_cpu", high_cpu_alert, "warning", 5)
alert_manager.add_alert_rule("high_memory", high_memory_alert, "critical", 5)
alert_manager.add_alert_rule("slow_response", slow_response_alert, "warning", 10)


def start_monitoring():
    """Start all monitoring components"""
    metrics_collector.start_collection()
    osint_logger.logger.info("Monitoring system started")


def stop_monitoring():
    """Stop all monitoring components"""
    metrics_collector.stop_collection()
    osint_logger.logger.info("Monitoring system stopped")
