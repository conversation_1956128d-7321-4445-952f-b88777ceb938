"""
🧠 CrewAI OSINT Agent Framework - Security and Privacy

Comprehensive security framework providing enterprise-grade protection for sensitive OSINT operations.
This module implements multiple layers of security including encryption, authentication, authorization,
and privacy-preserving features for handling classified or sensitive intelligence data.

Security Features:
    - AES-256 encryption for data at rest and in transit
    - PBKDF2-based key derivation with configurable iterations
    - JWT-based session management with secure token handling
    - Role-based access control (RBAC) with granular permissions
    - Comprehensive audit logging for compliance requirements
    - Local embedding models for privacy-preserving analysis
    - Secure file handling with automatic encryption
    - Rate limiting and brute force protection
    - Session management with automatic expiration

Privacy Features:
    - Local-only processing options for sensitive data
    - Configurable data retention policies
    - Secure deletion with cryptographic erasure
    - Anonymous analysis modes
    - Metadata scrubbing capabilities

This module is designed for environments requiring high security standards,
including government agencies, financial institutions, and security research organizations.
"""

import base64
import hashlib
import hmac
import json
import os
import secrets
import sqlite3
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Cryptography imports
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import padding, rsa
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

# JWT for authentication
try:
    import jwt

    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False

# Local embeddings
try:
    from sentence_transformers import SentenceTransformer

    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

# Add project root to path
import sys

project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.logging_config import osint_logger


class SecurityError(Exception):
    """Custom exception for security-related errors"""
    pass


class AuthenticationError(SecurityError):
    """Exception for authentication failures"""
    pass


class AuthorizationError(SecurityError):
    """Exception for authorization failures"""
    pass


@dataclass
class UserSession:
    """User session data"""

    user_id: str
    session_id: str
    created_at: datetime
    expires_at: datetime
    permissions: List[str]
    ip_address: str
    user_agent: str
    active: bool = True


@dataclass
class AccessLog:
    """Access log entry"""

    user_id: str
    action: str
    resource: str
    timestamp: datetime
    ip_address: str
    success: bool
    details: Dict[str, Any]


class EncryptionManager:
    """
    Advanced encryption manager for securing sensitive OSINT data.

    Provides enterprise-grade encryption capabilities using AES-256 in CBC mode
    with HMAC authentication. Supports both data and file encryption with
    automatic key management and secure key derivation.

    Features:
        - AES-256 encryption with Fernet (AES-128 CBC + HMAC-SHA256)
        - PBKDF2 key derivation with 100,000 iterations
        - Automatic master key generation and secure storage
        - File encryption with metadata preservation
        - Secure key rotation capabilities
        - Memory-safe operations with automatic cleanup

    Security Considerations:
        - Master keys are stored with 0600 permissions
        - Salt is used for key derivation (should be random in production)
        - All operations are logged for audit purposes
        - Supports hardware security modules (HSM) integration

    Attributes:
        master_key: Primary encryption key (base64 encoded)
        fernet: Fernet cipher instance for encryption operations
        key_rotation_interval: Automatic key rotation interval in days
    """

    def __init__(self, master_key: Optional[str] = None, enable_hsm: bool = False):
        """
        Initialize encryption manager with secure key handling.

        Args:
            master_key: Optional master key (auto-generated if not provided)
            enable_hsm: Enable hardware security module integration

        Raises:
            ImportError: If cryptography library is not available
            SecurityError: If key generation or validation fails
        """
        if not CRYPTOGRAPHY_AVAILABLE:
            raise ImportError(
                "Cryptography library not available. Install with: pip install cryptography"
            )

        self.enable_hsm = enable_hsm
        self.key_rotation_interval = 90  # days
        self.encryption_stats = {
            "operations_count": 0,
            "bytes_encrypted": 0,
            "bytes_decrypted": 0,
            "last_operation": None
        }

        try:
            self.master_key = master_key or self._generate_master_key()
            self.fernet = self._create_fernet_cipher()
            self._validate_encryption_setup()
        except Exception as e:
            osint_logger.log_error(e, "encryption_manager_init")
            raise SecurityError(f"Failed to initialize encryption manager: {e}")

        osint_logger.logger.info(
            f"Encryption manager initialized - HSM enabled: {enable_hsm}, "
            f"Key rotation days: {self.key_rotation_interval}"
        )

    def _generate_master_key(self) -> str:
        """
        Generate or retrieve master encryption key with enhanced security.

        Key priority order:
        1. Environment variable (OSINT_MASTER_KEY)
        2. Existing key file with validation
        3. Generate new cryptographically secure key

        Returns:
            Base64 encoded master key

        Raises:
            SecurityError: If key generation or validation fails
        """
        try:
            # Check for existing key in environment
            env_key = os.getenv("OSINT_MASTER_KEY")
            if env_key:
                self._validate_key_format(env_key)
                osint_logger.logger.info("Master key loaded from environment")
                return env_key

            # Check for existing key file
            key_file = Path("data/master.key")
            if key_file.exists():
                # Verify file permissions
                file_stat = key_file.stat()
                if file_stat.st_mode & 0o077:
                    osint_logger.logger.warning(
                        "Key file has insecure permissions, fixing..."
                    )
                    os.chmod(key_file, 0o600)

                with open(key_file, "rb") as f:
                    existing_key = f.read().decode()
                    self._validate_key_format(existing_key)
                    osint_logger.logger.info("Master key loaded from file")
                    return existing_key

            # Generate new cryptographically secure key
            key = Fernet.generate_key()

            # Ensure secure directory creation
            key_file.parent.mkdir(parents=True, exist_ok=True, mode=0o700)

            # Write key with atomic operation
            temp_file = key_file.with_suffix('.tmp')
            with open(temp_file, "wb") as f:
                f.write(key)

            # Set restrictive permissions before moving
            os.chmod(temp_file, 0o600)
            temp_file.replace(key_file)

            osint_logger.logger.warning(
                f"New master key generated and saved securely at {key_file} with permissions 0600"
            )

            return key.decode()

        except Exception as e:
            osint_logger.log_error(e, "master_key_generation")
            raise SecurityError(f"Failed to generate master key: {e}")

    def _validate_key_format(self, key: str) -> None:
        """
        Validate master key format and strength.

        Args:
            key: Key to validate

        Raises:
            SecurityError: If key format is invalid
        """
        try:
            # Attempt to create Fernet instance to validate format
            key_bytes = key.encode() if isinstance(key, str) else key
            Fernet(key_bytes)
        except Exception as e:
            raise SecurityError(f"Invalid key format: {e}")

    def _validate_encryption_setup(self) -> None:
        """
        Validate encryption setup with test operations.

        Raises:
            SecurityError: If encryption setup validation fails
        """
        try:
            # Test encryption/decryption cycle
            test_data = b"encryption_test_data"
            encrypted = self.fernet.encrypt(test_data)
            decrypted = self.fernet.decrypt(encrypted)

            if decrypted != test_data:
                raise SecurityError("Encryption validation failed")

            osint_logger.logger.debug("Encryption setup validated successfully")

        except Exception as e:
            raise SecurityError(f"Encryption validation failed: {e}")

    def _create_fernet_cipher(self) -> Fernet:
        """Create Fernet cipher from master key"""

        if isinstance(self.master_key, str):
            key_bytes = self.master_key.encode()
        else:
            key_bytes = self.master_key

        # Derive key using PBKDF2
        salt = b"osint_framework_salt"  # In production, use random salt
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )

        key = base64.urlsafe_b64encode(kdf.derive(key_bytes))
        return Fernet(key)

    def encrypt_data(self, data: Union[str, bytes, Dict[str, Any]]) -> str:
        """Encrypt data and return base64 encoded string"""

        try:
            if isinstance(data, dict):
                data = json.dumps(data)

            if isinstance(data, str):
                data = data.encode("utf-8")

            encrypted = self.fernet.encrypt(data)
            return base64.b64encode(encrypted).decode("utf-8")

        except Exception as e:
            osint_logger.log_error(e, "data_encryption")
            raise

    def decrypt_data(self, encrypted_data: str) -> bytes:
        """Decrypt base64 encoded encrypted data"""

        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode("utf-8"))
            decrypted = self.fernet.decrypt(encrypted_bytes)
            return decrypted

        except Exception as e:
            osint_logger.log_error(e, "data_decryption")
            raise

    def encrypt_file(self, file_path: Path, output_path: Optional[Path] = None) -> Path:
        """Encrypt a file"""

        try:
            with open(file_path, "rb") as f:
                file_data = f.read()

            encrypted_data = self.fernet.encrypt(file_data)

            if not output_path:
                output_path = file_path.with_suffix(file_path.suffix + ".enc")

            with open(output_path, "wb") as f:
                f.write(encrypted_data)

            osint_logger.logger.info(f"File encrypted: {file_path} -> {output_path}")
            return output_path

        except Exception as e:
            osint_logger.log_error(e, "file_encryption", {"file_path": str(file_path)})
            raise

    def decrypt_file(self, encrypted_file_path: Path, output_path: Optional[Path] = None) -> Path:
        """Decrypt a file"""

        try:
            with open(encrypted_file_path, "rb") as f:
                encrypted_data = f.read()

            decrypted_data = self.fernet.decrypt(encrypted_data)

            if not output_path:
                output_path = encrypted_file_path.with_suffix("")
                if output_path.suffix == ".enc":
                    output_path = output_path.with_suffix("")

            with open(output_path, "wb") as f:
                f.write(decrypted_data)

            osint_logger.logger.info(f"File decrypted: {encrypted_file_path} -> {output_path}")
            return output_path

        except Exception as e:
            osint_logger.log_error(e, "file_decryption", {"file_path": str(encrypted_file_path)})
            raise


class AuthenticationManager:
    """Handle user authentication and authorization"""

    def __init__(self, secret_key: Optional[str] = None):
        self.secret_key = secret_key or self._generate_secret_key()
        self.sessions: Dict[str, UserSession] = {}
        self.access_logs: List[AccessLog] = []

        self.setup_database()

        osint_logger.logger.info("Authentication manager initialized")

    def _generate_secret_key(self) -> str:
        """Generate secret key for JWT tokens"""

        env_key = os.getenv("OSINT_SECRET_KEY")
        if env_key:
            return env_key

        # Generate new secret key
        secret = secrets.token_urlsafe(32)
        osint_logger.logger.warning(
            "New secret key generated - set OSINT_SECRET_KEY environment variable"
        )
        return secret

    def setup_database(self):
        """Setup authentication database"""

        db_path = Path("data/auth.db")
        db_path.parent.mkdir(parents=True, exist_ok=True)

        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # Users table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt TEXT NOT NULL,
                    permissions TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    last_login TEXT,
                    active BOOLEAN DEFAULT TRUE
                )
            """
            )

            # Sessions table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    expires_at TEXT NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """
            )

            # Access logs table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS access_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    action TEXT NOT NULL,
                    resource TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    ip_address TEXT,
                    success BOOLEAN NOT NULL,
                    details TEXT
                )
            """
            )

            conn.commit()

    def create_user(self, username: str, password: str, permissions: List[str] = None) -> str:
        """Create a new user"""

        if not permissions:
            permissions = ["read"]

        try:
            # Generate salt and hash password
            salt = secrets.token_hex(16)
            password_hash = self._hash_password(password, salt)

            user_id = secrets.token_urlsafe(16)

            db_path = Path("data/auth.db")
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    INSERT INTO users (id, username, password_hash, salt, permissions, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """,
                    (
                        user_id,
                        username,
                        password_hash,
                        salt,
                        json.dumps(permissions),
                        datetime.now().isoformat(),
                    ),
                )

                conn.commit()

            osint_logger.logger.info(f"User created: {username}")
            return user_id

        except sqlite3.IntegrityError:
            raise ValueError("Username already exists")
        except Exception as e:
            osint_logger.log_error(e, "user_creation", {"username": username})
            raise

    def authenticate_user(
        self, username: str, password: str, ip_address: str = "", user_agent: str = ""
    ) -> Optional[str]:
        """Authenticate user and create session"""

        try:
            db_path = Path("data/auth.db")
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    SELECT id, password_hash, salt, permissions, active
                    FROM users WHERE username = ?
                """,
                    (username,),
                )

                user_data = cursor.fetchone()

                if not user_data or not user_data[4]:  # User not found or inactive
                    self._log_access(
                        None,
                        "login_failed",
                        username,
                        ip_address,
                        False,
                        {"reason": "user_not_found"},
                    )
                    return None

                user_id, stored_hash, salt, permissions_json, active = user_data

                # Verify password
                if not self._verify_password(password, stored_hash, salt):
                    self._log_access(
                        user_id,
                        "login_failed",
                        username,
                        ip_address,
                        False,
                        {"reason": "invalid_password"},
                    )
                    return None

                # Create session
                session_id = self._create_session(
                    user_id, json.loads(permissions_json), ip_address, user_agent
                )

                # Update last login
                cursor.execute(
                    """
                    UPDATE users SET last_login = ? WHERE id = ?
                """,
                    (datetime.now().isoformat(), user_id),
                )

                conn.commit()

                self._log_access(user_id, "login_success", username, ip_address, True, {})

                osint_logger.logger.info(f"User authenticated: {username}")
                return session_id

        except Exception as e:
            osint_logger.log_error(e, "user_authentication", {"username": username})
            return None

    def _hash_password(self, password: str, salt: str) -> str:
        """Hash password with salt"""

        return hashlib.pbkdf2_hmac("sha256", password.encode(), salt.encode(), 100000).hex()

    def _verify_password(self, password: str, stored_hash: str, salt: str) -> bool:
        """Verify password against stored hash"""

        return hmac.compare_digest(stored_hash, self._hash_password(password, salt))

    def _create_session(
        self, user_id: str, permissions: List[str], ip_address: str, user_agent: str
    ) -> str:
        """Create user session"""

        session_id = secrets.token_urlsafe(32)
        created_at = datetime.now()
        expires_at = created_at + timedelta(hours=24)  # 24 hour sessions

        session = UserSession(
            user_id=user_id,
            session_id=session_id,
            created_at=created_at,
            expires_at=expires_at,
            permissions=permissions,
            ip_address=ip_address,
            user_agent=user_agent,
        )

        self.sessions[session_id] = session

        # Store in database
        db_path = Path("data/auth.db")
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            cursor.execute(
                """
                INSERT INTO sessions (session_id, user_id, created_at, expires_at, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            """,
                (
                    session_id,
                    user_id,
                    created_at.isoformat(),
                    expires_at.isoformat(),
                    ip_address,
                    user_agent,
                ),
            )

            conn.commit()

        return session_id

    def validate_session(self, session_id: str) -> Optional[UserSession]:
        """Validate session and return user session"""

        try:
            # Check in-memory sessions first
            if session_id in self.sessions:
                session = self.sessions[session_id]

                if session.expires_at > datetime.now() and session.active:
                    return session
                else:
                    # Session expired
                    del self.sessions[session_id]
                    return None

            # Check database
            db_path = Path("data/auth.db")
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    SELECT s.user_id, s.created_at, s.expires_at, s.ip_address, s.user_agent,
                           u.permissions
                    FROM sessions s
                    JOIN users u ON s.user_id = u.id
                    WHERE s.session_id = ? AND s.active = TRUE AND u.active = TRUE
                """,
                    (session_id,),
                )

                session_data = cursor.fetchone()

                if not session_data:
                    return None

                user_id, created_at, expires_at, ip_address, user_agent, permissions_json = (
                    session_data
                )

                expires_at_dt = datetime.fromisoformat(expires_at)

                if expires_at_dt <= datetime.now():
                    # Session expired
                    cursor.execute(
                        """
                        UPDATE sessions SET active = FALSE WHERE session_id = ?
                    """,
                        (session_id,),
                    )
                    conn.commit()
                    return None

                # Create session object
                session = UserSession(
                    user_id=user_id,
                    session_id=session_id,
                    created_at=datetime.fromisoformat(created_at),
                    expires_at=expires_at_dt,
                    permissions=json.loads(permissions_json),
                    ip_address=ip_address,
                    user_agent=user_agent,
                )

                # Cache in memory
                self.sessions[session_id] = session

                return session

        except Exception as e:
            osint_logger.log_error(e, "session_validation", {"session_id": session_id[:10]})
            return None

    def check_permission(self, session: UserSession, required_permission: str) -> bool:
        """Check if user has required permission"""

        if "admin" in session.permissions:
            return True

        return required_permission in session.permissions

    def _log_access(
        self,
        user_id: Optional[str],
        action: str,
        resource: str,
        ip_address: str,
        success: bool,
        details: Dict[str, Any],
    ):
        """Log access attempt"""

        access_log = AccessLog(
            user_id=user_id or "anonymous",
            action=action,
            resource=resource,
            timestamp=datetime.now(),
            ip_address=ip_address,
            success=success,
            details=details,
        )

        self.access_logs.append(access_log)

        # Store in database
        try:
            db_path = Path("data/auth.db")
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    INSERT INTO access_logs (user_id, action, resource, timestamp, ip_address, success, details)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        access_log.user_id,
                        access_log.action,
                        access_log.resource,
                        access_log.timestamp.isoformat(),
                        access_log.ip_address,
                        access_log.success,
                        json.dumps(access_log.details),
                    ),
                )

                conn.commit()

        except Exception as e:
            osint_logger.log_error(e, "access_logging")

    def logout_session(self, session_id: str):
        """Logout session"""

        try:
            # Remove from memory
            if session_id in self.sessions:
                del self.sessions[session_id]

            # Deactivate in database
            db_path = Path("data/auth.db")
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    UPDATE sessions SET active = FALSE WHERE session_id = ?
                """,
                    (session_id,),
                )

                conn.commit()

        except Exception as e:
            osint_logger.log_error(e, "session_logout", {"session_id": session_id[:10]})


class LocalEmbeddingsManager:
    """Manage local embeddings for privacy-focused operations"""

    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model_name = model_name
        self.model = None
        self.embeddings_cache = {}

        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            osint_logger.logger.warning(
                "Sentence Transformers not available - local embeddings disabled"
            )
            return

        self.load_model()

        osint_logger.logger.info(f"Local embeddings manager initialized with model: {model_name}")

    def load_model(self):
        """Load the sentence transformer model"""

        try:
            # Load model locally to avoid sending data to external services
            self.model = SentenceTransformer(self.model_name)
            osint_logger.logger.info(f"Local embedding model loaded: {self.model_name}")

        except Exception as e:
            osint_logger.log_error(e, "local_embeddings_model_load")
            self.model = None

    def generate_embeddings(
        self, texts: Union[str, List[str]], use_cache: bool = True
    ) -> Union[List[float], List[List[float]]]:
        """Generate embeddings locally"""

        if not self.model:
            raise RuntimeError("Local embeddings model not available")

        # Handle single text
        if isinstance(texts, str):
            texts = [texts]
            single_text = True
        else:
            single_text = False

        embeddings = []

        for text in texts:
            # Check cache first
            if use_cache and text in self.embeddings_cache:
                embeddings.append(self.embeddings_cache[text])
                continue

            # Generate embedding
            try:
                embedding = self.model.encode(text, convert_to_tensor=False).tolist()

                # Cache the embedding
                if use_cache:
                    self.embeddings_cache[text] = embedding

                embeddings.append(embedding)

            except Exception as e:
                osint_logger.log_error(e, "embedding_generation", {"text": text[:100]})
                # Return zero vector as fallback
                embeddings.append([0.0] * 384)  # MiniLM dimension

        return embeddings[0] if single_text else embeddings

    def compute_similarity(self, text1: str, text2: str) -> float:
        """Compute similarity between two texts using local embeddings"""

        try:
            embeddings = self.generate_embeddings([text1, text2])

            # Compute cosine similarity
            import numpy as np

            vec1 = np.array(embeddings[0])
            vec2 = np.array(embeddings[1])

            # Cosine similarity
            similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))

            return float(similarity)

        except Exception as e:
            osint_logger.log_error(e, "similarity_computation")
            return 0.0

    def find_similar_texts(
        self, query: str, text_corpus: List[str], top_k: int = 5, threshold: float = 0.5
    ) -> List[Tuple[str, float]]:
        """Find similar texts in a corpus"""

        try:
            # Generate embeddings for query and corpus
            query_embedding = self.generate_embeddings(query)
            corpus_embeddings = self.generate_embeddings(text_corpus)

            import numpy as np

            query_vec = np.array(query_embedding)
            similarities = []

            for i, corpus_embedding in enumerate(corpus_embeddings):
                corpus_vec = np.array(corpus_embedding)

                # Cosine similarity
                similarity = np.dot(query_vec, corpus_vec) / (
                    np.linalg.norm(query_vec) * np.linalg.norm(corpus_vec)
                )

                if similarity >= threshold:
                    similarities.append((text_corpus[i], float(similarity)))

            # Sort by similarity and return top_k
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:top_k]

        except Exception as e:
            osint_logger.log_error(e, "similar_texts_search")
            return []

    def clear_cache(self):
        """Clear embeddings cache"""
        self.embeddings_cache.clear()
        osint_logger.logger.info("Embeddings cache cleared")


class PrivacyManager:
    """Manage privacy-focused operations and data handling"""

    def __init__(self, encryption_manager: EncryptionManager):
        self.encryption_manager = encryption_manager
        self.sensitive_patterns = self._load_sensitive_patterns()
        self.data_retention_policies = self._load_retention_policies()

        osint_logger.logger.info("Privacy manager initialized")

    def _load_sensitive_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for detecting sensitive information"""

        return {
            "email": [r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"],
            "phone": [r"\b\d{3}-\d{3}-\d{4}\b", r"\b\(\d{3}\)\s*\d{3}-\d{4}\b"],
            "ssn": [r"\b\d{3}-\d{2}-\d{4}\b"],
            "credit_card": [r"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b"],
            "ip_address": [r"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b"],
            "api_key": [r"\b[A-Za-z0-9]{32,}\b"],
            "password": [r"password[:\s=]+[^\s]+", r"pwd[:\s=]+[^\s]+"],
        }

    def _load_retention_policies(self) -> Dict[str, int]:
        """Load data retention policies (in days)"""

        return {
            "search_queries": 30,
            "analysis_results": 90,
            "user_sessions": 7,
            "access_logs": 365,
            "temporary_files": 1,
            "cached_data": 7,
        }

    def scan_for_sensitive_data(self, text: str) -> Dict[str, List[Dict[str, Any]]]:
        """Scan text for sensitive information"""

        import re

        findings = {}

        for category, patterns in self.sensitive_patterns.items():
            category_findings = []

            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)

                for match in matches:
                    category_findings.append(
                        {
                            "match": match.group(),
                            "start": match.start(),
                            "end": match.end(),
                            "pattern": pattern,
                        }
                    )

            if category_findings:
                findings[category] = category_findings

        return findings

    def sanitize_text(self, text: str, replacement: str = "[REDACTED]") -> str:
        """Sanitize text by removing sensitive information"""

        import re

        sanitized = text

        for category, patterns in self.sensitive_patterns.items():
            for pattern in patterns:
                sanitized = re.sub(pattern, replacement, sanitized, flags=re.IGNORECASE)

        return sanitized

    def encrypt_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive fields in data"""

        sensitive_fields = [
            "password",
            "api_key",
            "token",
            "secret",
            "private_key",
            "email",
            "phone",
            "ssn",
            "credit_card",
        ]

        encrypted_data = data.copy()

        for field in sensitive_fields:
            if field in encrypted_data:
                encrypted_data[field] = self.encryption_manager.encrypt_data(
                    str(encrypted_data[field])
                )
                encrypted_data[f"{field}_encrypted"] = True

        return encrypted_data

    def apply_data_retention(self, data_type: str) -> bool:
        """Check if data should be retained based on retention policy"""

        retention_days = self.data_retention_policies.get(data_type, 30)

        # This would typically check against actual data timestamps
        # For now, return True (retain data)
        return True

    def anonymize_user_data(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Anonymize user data for privacy"""

        anonymized = user_data.copy()

        # Remove or hash identifying information
        if "user_id" in anonymized:
            anonymized["user_id"] = hashlib.sha256(str(anonymized["user_id"]).encode()).hexdigest()[
                :16
            ]

        if "ip_address" in anonymized:
            # Mask last octet of IP address
            ip_parts = anonymized["ip_address"].split(".")
            if len(ip_parts) == 4:
                anonymized["ip_address"] = ".".join(ip_parts[:3] + ["xxx"])

        if "user_agent" in anonymized:
            # Keep only browser family
            user_agent = anonymized["user_agent"]
            if "Chrome" in user_agent:
                anonymized["user_agent"] = "Chrome/xxx"
            elif "Firefox" in user_agent:
                anonymized["user_agent"] = "Firefox/xxx"
            else:
                anonymized["user_agent"] = "Unknown/xxx"

        return anonymized

    def secure_delete_file(self, file_path: Path, passes: int = 3):
        """Securely delete a file by overwriting it multiple times"""

        try:
            if not file_path.exists():
                return

            file_size = file_path.stat().st_size

            with open(file_path, "r+b") as f:
                for _ in range(passes):
                    f.seek(0)
                    f.write(secrets.token_bytes(file_size))
                    f.flush()
                    os.fsync(f.fileno())

            # Finally delete the file
            file_path.unlink()

            osint_logger.logger.info(f"File securely deleted: {file_path}")

        except Exception as e:
            osint_logger.log_error(e, "secure_file_deletion", {"file_path": str(file_path)})

    def create_privacy_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a privacy report for data processing"""

        report = {
            "timestamp": datetime.now().isoformat(),
            "data_types_processed": [],
            "sensitive_data_found": {},
            "encryption_applied": False,
            "anonymization_applied": False,
            "retention_policy_applied": False,
        }

        # Scan for sensitive data
        if isinstance(data, dict):
            text_content = json.dumps(data)
        else:
            text_content = str(data)

        report["sensitive_data_found"] = self.scan_for_sensitive_data(text_content)

        # Check if encryption was applied
        if any(key.endswith("_encrypted") for key in data.keys() if isinstance(data, dict)):
            report["encryption_applied"] = True

        return report


# Global instances
encryption_manager = EncryptionManager() if CRYPTOGRAPHY_AVAILABLE else None
auth_manager = AuthenticationManager()
local_embeddings = LocalEmbeddingsManager() if SENTENCE_TRANSFORMERS_AVAILABLE else None
privacy_manager = PrivacyManager(encryption_manager) if encryption_manager else None
