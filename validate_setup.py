#!/usr/bin/env python3
"""
🧠 CrewAI OSINT Agent Framework - Setup Validator

Comprehensive validation script to check framework configuration and readiness.
Run this script to ensure your environment is properly configured.
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(project_root / ".env")

from utils.config_validator import validate_framework_config, check_quick_status
from utils.error_handling import create_error_report

def print_header():
    """Print validation header"""
    print("=" * 70)
    print("🧠 CrewAI OSINT Agent Framework - Setup Validation")
    print("=" * 70)
    print()

def print_section(title: str):
    """Print section header"""
    print(f"\n📋 {title}")
    print("-" * (len(title) + 4))

def print_result(message: str, status: str = "info"):
    """Print formatted result"""
    icons = {
        "success": "✅",
        "error": "❌", 
        "warning": "⚠️",
        "info": "ℹ️"
    }
    icon = icons.get(status, "•")
    print(f"{icon} {message}")

def run_quick_validation():
    """Run quick validation checks"""
    print_section("Quick Status Check")
    
    is_ready, issues = check_quick_status()
    
    if is_ready:
        print_result("Framework is ready for basic operations!", "success")
    else:
        print_result("Framework has configuration issues:", "error")
        for issue in issues:
            print_result(f"  - {issue}", "error")
    
    return is_ready

def run_comprehensive_validation():
    """Run comprehensive validation"""
    print_section("Comprehensive Validation")
    
    try:
        report = validate_framework_config()
        
        # Print summary
        summary = report["summary"]
        print_result(f"Total checks: {summary['total_checks']}")
        print_result(f"Errors: {summary['errors']}", "error" if summary['errors'] > 0 else "info")
        print_result(f"Warnings: {summary['warnings']}", "warning" if summary['warnings'] > 0 else "info")
        print_result(f"Overall status: {summary['overall_status']}", 
                    "success" if summary['overall_status'] == "PASS" else "error")
        
        # Print detailed results
        if report["results"]["errors"]:
            print_section("❌ Errors (Must Fix)")
            for error in report["results"]["errors"]:
                print_result(error["message"], "error")
                if error.get("details"):
                    for key, value in error["details"].items():
                        print(f"    {key}: {value}")
        
        if report["results"]["warnings"]:
            print_section("⚠️ Warnings (Recommended)")
            for warning in report["results"]["warnings"]:
                print_result(warning["message"], "warning")
        
        # Print recommendations
        if report["recommendations"]:
            print_section("💡 Recommendations")
            for rec in report["recommendations"]:
                print(f"  {rec}")
        
        return summary['overall_status'] == "PASS"
        
    except Exception as e:
        print_result(f"Validation failed: {str(e)}", "error")
        return False

def test_basic_functionality():
    """Test basic framework functionality"""
    print_section("Basic Functionality Tests")
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Import core modules
    total_tests += 1
    try:
        from agents.base_agent import BaseOSINTAgent
        from agents.geo_agent import GeopoliticalAgent
        from agents.cti_agent import CTIAgent
        print_result("Core agent imports successful", "success")
        tests_passed += 1
    except Exception as e:
        print_result(f"Core agent imports failed: {str(e)}", "error")
    
    # Test 2: Import tools
    total_tests += 1
    try:
        from tools.serper_wrapper import SerperSearchTool
        from tools.llamaindex_tools import LlamaIndexRAGTool
        print_result("Tool imports successful", "success")
        tests_passed += 1
    except Exception as e:
        print_result(f"Tool imports failed: {str(e)}", "error")
    
    # Test 3: Import workflows
    total_tests += 1
    try:
        from workflows.langchain_geo_workflow import GeopoliticalWorkflow
        from workflows.llamaindex_cti_workflow import CTIWorkflow
        print_result("Workflow imports successful", "success")
        tests_passed += 1
    except Exception as e:
        print_result(f"Workflow imports failed: {str(e)}", "error")
    
    # Test 4: Test API key validation (if keys are present)
    total_tests += 1
    if os.getenv('OPENAI_API_KEY') and os.getenv('SERPER_API_KEY'):
        try:
            # Try to create a simple agent (without full initialization)
            print_result("API keys are configured", "success")
            tests_passed += 1
        except Exception as e:
            print_result(f"API key validation failed: {str(e)}", "error")
    else:
        print_result("API keys not configured - skipping test", "warning")
    
    # Test 5: Test error handling
    total_tests += 1
    try:
        from utils.error_handling import OSINTFrameworkError, validate_api_keys
        from utils.config_validator import ConfigValidator
        print_result("Error handling modules working", "success")
        tests_passed += 1
    except Exception as e:
        print_result(f"Error handling test failed: {str(e)}", "error")
    
    print()
    print_result(f"Basic functionality tests: {tests_passed}/{total_tests} passed", 
                "success" if tests_passed == total_tests else "warning")
    
    return tests_passed == total_tests

def generate_setup_report():
    """Generate a detailed setup report"""
    print_section("Generating Setup Report")
    
    try:
        report = validate_framework_config()
        
        # Add system information
        report["system_info"] = {
            "python_version": sys.version,
            "platform": sys.platform,
            "project_root": str(project_root),
            "validation_time": datetime.now().isoformat()
        }
        
        # Save report
        report_file = project_root / "setup_validation_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print_result(f"Setup report saved to: {report_file}", "success")
        return str(report_file)
        
    except Exception as e:
        print_result(f"Failed to generate setup report: {str(e)}", "error")
        return None

def print_next_steps(all_passed: bool):
    """Print next steps based on validation results"""
    print_section("Next Steps")
    
    if all_passed:
        print("🎉 Congratulations! Your OSINT framework is ready to use.")
        print()
        print("You can now:")
        print("  • Run the Streamlit UI: python run_ui.py")
        print("  • Start the API server: python run_api.py")
        print("  • Try the examples in the examples/ directory")
        print("  • Read the documentation in README.md")
    else:
        print("🔧 Please fix the issues above before using the framework.")
        print()
        print("Common fixes:")
        print("  • Create .env file with API keys")
        print("  • Install missing packages: pip install -r requirements.txt")
        print("  • Ensure you're in the correct project directory")
        print("  • Check file permissions for data directories")

def main():
    """Main validation function"""
    print_header()
    
    # Run validations
    quick_passed = run_quick_validation()
    comprehensive_passed = run_comprehensive_validation()
    functionality_passed = test_basic_functionality()
    
    # Generate report
    report_file = generate_setup_report()
    
    # Determine overall status
    all_passed = quick_passed and comprehensive_passed and functionality_passed
    
    # Print summary
    print_section("Validation Summary")
    print_result(f"Quick validation: {'PASS' if quick_passed else 'FAIL'}", 
                "success" if quick_passed else "error")
    print_result(f"Comprehensive validation: {'PASS' if comprehensive_passed else 'FAIL'}", 
                "success" if comprehensive_passed else "error")
    print_result(f"Functionality tests: {'PASS' if functionality_passed else 'FAIL'}", 
                "success" if functionality_passed else "error")
    print()
    print_result(f"Overall status: {'READY' if all_passed else 'NOT READY'}", 
                "success" if all_passed else "error")
    
    # Print next steps
    print_next_steps(all_passed)
    
    # Exit with appropriate code
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Validation failed with unexpected error: {str(e)}")
        
        # Create error report
        error_report = create_error_report(e, {"script": "validate_setup.py"})
        error_file = project_root / "validation_error.json"
        
        try:
            with open(error_file, 'w') as f:
                json.dump(error_report, f, indent=2)
            print(f"Error report saved to: {error_file}")
        except:
            pass
        
        sys.exit(1)
