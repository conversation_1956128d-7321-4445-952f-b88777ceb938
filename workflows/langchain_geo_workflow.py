"""
LangChain workflow for geopolitical intelligence analysis.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain.prompts import ChatPromptTemplate, PromptTemplate
from langchain.schema import BaseMessage, HumanMessage, SystemMessage
from langchain.tools import BaseTool
from langchain_openai import ChatOpenAI

from agents.geo_agent import GeopoliticalAgent
from tools.serper_wrapper import SerperNewsSearchTool, SerperSearchTool


class GeopoliticalWorkflow:
    """
    LangChain-based workflow for comprehensive geopolitical analysis.

    Orchestrates multiple tools and analysis steps to provide
    comprehensive geopolitical intelligence reports.
    """

    def __init__(self, llm_model: str = "gpt-4", temperature: float = 0.1, verbose: bool = True):
        """
        Initialize the geopolitical workflow.

        Args:
            llm_model: LLM model to use
            temperature: LLM temperature setting
            verbose: Enable verbose logging
        """
        self.llm = ChatOpenAI(model=llm_model, temperature=temperature)
        self.verbose = verbose
        self.logger = logging.getLogger(__name__)

        # Initialize tools
        self.tools = self._initialize_tools()

        # Initialize chains
        self._setup_chains()

        # Initialize agent
        self.geo_agent = GeopoliticalAgent()

    def _initialize_tools(self) -> List[BaseTool]:
        """Initialize tools for the workflow."""
        tools = []

        try:
            tools.append(SerperSearchTool())
            tools.append(SerperNewsSearchTool())
        except ValueError as e:
            self.logger.warning(f"Serper tools not available: {e}")

        return tools

    def _setup_chains(self):
        """Setup LangChain chains for different analysis steps."""

        # Information gathering chain
        info_gathering_prompt = PromptTemplate(
            input_variables=["topic", "time_range", "regions"],
            template="""
            You are a geopolitical intelligence analyst. Your task is to gather comprehensive
            information about the following topic:

            Topic: {topic}
            Time Range: {time_range}
            Regions of Interest: {regions}

            Please identify the key search queries and information sources needed to analyze this topic.
            Focus on:
            1. Recent developments and news
            2. Official statements and policy documents
            3. Expert analysis and commentary
            4. Historical context and background

            Provide a structured list of search queries and source types.
            """,
        )

        self.info_gathering_chain = info_gathering_prompt | self.llm

        # Analysis synthesis chain
        synthesis_prompt = PromptTemplate(
            input_variables=["topic", "gathered_info", "context"],
            template="""
            You are a senior geopolitical analyst. Based on the gathered information below,
            provide a comprehensive analysis of the following topic:

            Topic: {topic}
            Context: {context}

            Gathered Information:
            {gathered_info}

            Please provide:
            1. Executive Summary (2-3 sentences)
            2. Key Developments and Trends
            3. Stakeholder Analysis
            4. Implications and Potential Outcomes
            5. Risk Assessment
            6. Recommendations for Monitoring

            Ensure your analysis is objective, well-sourced, and considers multiple perspectives.
            """,
        )

        self.synthesis_chain = synthesis_prompt | self.llm

        # Report generation chain
        report_prompt = PromptTemplate(
            input_variables=["topic", "analysis", "confidence_level"],
            template="""
            Generate a professional intelligence report based on the analysis below:

            Topic: {topic}
            Analysis: {analysis}
            Confidence Level: {confidence_level}

            Format the report as follows:

            # GEOPOLITICAL INTELLIGENCE REPORT

            ## EXECUTIVE SUMMARY
            [Brief overview of key findings]

            ## SITUATION ANALYSIS
            [Detailed analysis of current situation]

            ## KEY ACTORS AND STAKEHOLDERS
            [Analysis of involved parties and their interests]

            ## IMPLICATIONS AND OUTLOOK
            [Potential developments and implications]

            ## CONFIDENCE ASSESSMENT
            [Assessment of information reliability and analysis confidence]

            ## RECOMMENDATIONS
            [Actionable recommendations for decision-makers]

            ## SOURCES AND METHODOLOGY
            [Information about sources and analytical approach]

            Ensure the report is professional, objective, and actionable.
            """,
        )

        self.report_chain = report_prompt | self.llm

    def analyze_geopolitical_situation(
        self,
        topic: str,
        regions: List[str] = None,
        time_range: str = "7d",
        include_historical_context: bool = True,
        confidence_threshold: float = 0.7,
    ) -> Dict[str, Any]:
        """
        Perform comprehensive geopolitical situation analysis.

        Args:
            topic: Geopolitical topic to analyze
            regions: Specific regions to focus on
            time_range: Time range for analysis
            include_historical_context: Whether to include historical context
            confidence_threshold: Minimum confidence threshold for sources

        Returns:
            Comprehensive analysis results
        """
        self.logger.info(f"Starting geopolitical analysis: {topic}")

        try:
            # Step 1: Plan information gathering
            regions_str = ", ".join(regions) if regions else "Global"
            search_strategy = self.info_gathering_chain.invoke(
                {"topic": topic, "time_range": time_range, "regions": regions_str}
            ).content

            # Step 2: Gather information using tools
            gathered_info = self._gather_information(topic, search_strategy, time_range)

            # Step 3: Add historical context if requested
            context = ""
            if include_historical_context:
                context = self._gather_historical_context(topic, regions)

            # Step 4: Synthesize analysis
            analysis = self.synthesis_chain.invoke(
                {"topic": topic, "gathered_info": gathered_info, "context": context}
            ).content

            # Step 5: Assess confidence
            confidence_level = self._assess_confidence(gathered_info, confidence_threshold)

            # Step 6: Generate final report
            report = self.report_chain.invoke(
                {"topic": topic, "analysis": analysis, "confidence_level": confidence_level}
            ).content

            # Package results
            result = {
                "topic": topic,
                "regions": regions,
                "time_range": time_range,
                "search_strategy": search_strategy,
                "gathered_information": gathered_info,
                "historical_context": context,
                "analysis": analysis,
                "confidence_level": confidence_level,
                "final_report": report,
                "timestamp": datetime.now().isoformat(),
                "workflow": "GeopoliticalWorkflow",
            }

            return result

        except Exception as e:
            self.logger.error(f"Error in geopolitical analysis: {str(e)}")
            return {"error": str(e), "topic": topic, "timestamp": datetime.now().isoformat()}

    def _gather_information(self, topic: str, search_strategy: str, time_range: str) -> str:
        """Gather information using available tools."""
        gathered_info = []

        # Extract search queries from strategy (simplified approach)
        # In a production system, you'd parse the strategy more sophisticatedly
        search_queries = [
            topic,
            f"{topic} recent developments",
            f"{topic} analysis {time_range}",
            f"{topic} policy implications",
        ]

        for tool in self.tools:
            for query in search_queries[:2]:  # Limit queries to avoid rate limits
                try:
                    if isinstance(tool, SerperNewsSearchTool):
                        result = tool._run(query=query, time_range=time_range, num_results=5)
                    else:
                        result = tool._run(query=query, num_results=5)

                    gathered_info.append(f"Query: {query}\nResults: {result}\n")

                except Exception as e:
                    self.logger.warning(f"Error with tool {tool.name}: {str(e)}")
                    continue

        return "\n".join(gathered_info)

    def _gather_historical_context(self, topic: str, regions: List[str] = None) -> str:
        """Gather historical context for the topic."""
        context_query = f"{topic} historical background"
        if regions:
            context_query += f" {' '.join(regions)}"

        # Use the geo agent for historical context
        try:
            historical_analysis = self.geo_agent.analyze(
                query=context_query, time_range="30d"  # Longer time range for context
            )
            return historical_analysis.get("analysis", "")
        except Exception as e:
            self.logger.warning(f"Error gathering historical context: {str(e)}")
            return "Historical context not available."

    def _assess_confidence(self, gathered_info: str, threshold: float) -> str:
        """Assess confidence level of the gathered information."""
        # Simple confidence assessment based on information quantity and quality
        info_length = len(gathered_info)

        if info_length > 5000:
            return "High"
        elif info_length > 2000:
            return "Medium"
        else:
            return "Low"

    def monitor_region(
        self, region: str, keywords: List[str] = None, update_frequency: str = "6h"
    ) -> Dict[str, Any]:
        """
        Set up continuous monitoring for a specific region.

        Args:
            region: Region to monitor
            keywords: Specific keywords to track
            update_frequency: How often to update

        Returns:
            Monitoring configuration and initial assessment
        """
        self.logger.info(f"Setting up monitoring for region: {region}")

        # Use the geo agent's monitoring capability
        monitoring_config = self.geo_agent.monitor_region(region, keywords)

        # Add workflow-specific configuration
        monitoring_config.update(
            {
                "workflow": "GeopoliticalWorkflow",
                "update_frequency": update_frequency,
                "tools_available": [tool.name for tool in self.tools],
            }
        )

        return monitoring_config

    def generate_situation_report(
        self, regions: List[str], time_range: str = "24h"
    ) -> Dict[str, Any]:
        """
        Generate a multi-region situation report.

        Args:
            regions: List of regions to include
            time_range: Time range for the report

        Returns:
            Comprehensive situation report
        """
        self.logger.info(f"Generating situation report for regions: {regions}")

        regional_analyses = {}

        for region in regions:
            try:
                analysis = self.analyze_geopolitical_situation(
                    topic=f"Current situation in {region}",
                    regions=[region],
                    time_range=time_range,
                    include_historical_context=False,
                )
                regional_analyses[region] = analysis
            except Exception as e:
                self.logger.error(f"Error analyzing {region}: {str(e)}")
                regional_analyses[region] = {"error": str(e)}

        # Generate summary report
        summary_prompt = f"""
        Based on the regional analyses below, provide a comprehensive situation report
        covering the following regions: {', '.join(regions)}

        Time Period: {time_range}

        Regional Analyses:
        {regional_analyses}

        Please provide:
        1. Overall Regional Stability Assessment
        2. Cross-Regional Trends and Connections
        3. Priority Areas for Attention
        4. Recommended Actions
        """

        summary = self.llm.predict(summary_prompt)

        return {
            "regions": regions,
            "time_range": time_range,
            "regional_analyses": regional_analyses,
            "summary_assessment": summary,
            "generated_at": datetime.now().isoformat(),
            "workflow": "GeopoliticalWorkflow",
        }
